/* Modern Material Design 3 Part Details Styles */

/* Base Styles for Scrolling */
html, body {
    height: auto;
    overflow-x: hidden;
    overflow-y: auto;
}

/* Main Content Container */
.main-content {
    margin-top: 80px;
    padding: 20px 20px 60px 20px;
    min-height: calc(100vh - 80px);
    overflow: visible;
}

/* Part Header - Enhanced Material Design 3 */
.part-header {
    background: linear-gradient(135deg, var(--surface-color) 0%, var(--md-grey-50) 100%);
    border-radius: 16px;
    padding: 24px 32px;
    margin-bottom: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
    border: 1px solid var(--md-grey-200);
    position: relative;
    overflow: hidden;
}

.part-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, #424242 100%);
}

.part-title-section {
    margin-bottom: 16px;
}

.part-title {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.part-number {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-color);
    font-family: 'Roboto Mono', monospace;
    letter-spacing: 0.5px;
}

.part-status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.75px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.part-status-badge.status-active {
    background: green;
    color: var(--md-white);
}

.part-description {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 16px;
    line-height: 1.4;
}

.part-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.meta-item {
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    background-color: var(--md-grey-100);
    border-radius: 8px;
    border: 1px solid var(--md-grey-200);
}

.meta-item .material-icons {
    font-size: 18px;
    margin-right: 8px;
    color: var(--primary-color);
}

.part-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}

/* Modern Material Design 3 Detail Tabs */
.detail-tabs {
    background: var(--surface-color);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
    overflow: hidden;
    border: 1px solid var(--md-grey-200);
}

.nav-tabs {
    border-bottom: none;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    padding: 8px;
    margin: 0;
    gap: 4px;
}

.nav-tabs .nav-item {
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    background: transparent;
    position: relative;
    overflow: hidden;
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.nav-tabs .nav-link:hover {
    background-color: var(--md-white);
    color: var(--primary-color);
    border-color: transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.nav-tabs .nav-link:hover::before {
    opacity: 1;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    border-color: transparent;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
    font-weight: 600;
}

.nav-tabs .nav-link.active::before {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 1;
}

.nav-tabs .nav-link .material-icons {
    font-size: 18px;
    margin-right: 8px;
}

/* Modern Tab Content */
.tab-content {
    padding: 0;
}

.tab-pane {
    padding: 24px 0;
}

/* Modern Material Design 3 Detail Cards */
.detail-card {
    background: var(--surface-color);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    border: 1px solid var(--md-grey-200);
}

.detail-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.detail-card .card-header {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 20px 24px;
    margin: 0;
    position: relative;
}

.detail-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
}

.detail-card .card-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 18px;
    display: flex;
    align-items: center;
    letter-spacing: 0.25px;
}

.detail-card .card-header .material-icons {
    color: var(--primary-color);
    margin-right: 8px;
}

.detail-card .card-body {
    padding: 24px;
}

/* Header Actions */
.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.header-actions .btn {
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 8px 16px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.header-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

/* Modern Info Groups */
.info-group {
    margin-bottom: 20px;
    position: relative;
}

.info-group label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 6px;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.75px;
}

.info-group label .text-danger {
    color: var(--status-inactive);
    font-weight: 600;
}

.info-value {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.4;
    padding: 8px 0;
    border-bottom: 1px solid var(--md-grey-100);
}

.info-value:last-child {
    border-bottom: none;
}

/* Enhanced Badges */
.info-value .badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
}

.info-value .badge.bg-success {
    background: linear-gradient(135deg, var(--status-active) 0%, #388e3c 100%);
    color: var(--md-white);
    border-color: var(--status-active);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.info-value .badge.bg-secondary {
    background-color: var(--md-grey-600);
    color: var(--md-white);
    border-color: var(--md-grey-600);
    box-shadow: 0 2px 8px rgba(158, 158, 158, 0.3);
}

/* Modern Stats */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--md-grey-100);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item:hover {
    background-color: var(--md-grey-50);
    margin: 0 -24px;
    padding: 16px 24px;
    border-radius: 8px;
}

.stat-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
}

/* Modern Activity Timeline */
.activity-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--md-grey-100);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: var(--md-grey-50);
    margin: 0 -24px;
    padding: 16px 24px;
    border-radius: 8px;
}

.activity-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--md-white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.activity-icon.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
}

.activity-icon.bg-success {
    background: linear-gradient(135deg, var(--status-active) 0%, #388e3c 100%);
}

.activity-icon.bg-warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-size: 14px;
}

.activity-time {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Modern Tables */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--md-grey-200);
}

.table {
    margin-bottom: 0;
    font-size: 14px;
}

.table thead th {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: 16px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.75px;
    position: relative;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 16px;
    right: 16px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
}

.table tbody td {
    padding: 16px;
    border-color: var(--md-grey-100);
    vertical-align: middle;
    font-size: 13px;
    color: var(--text-primary);
}

.table tbody tr {
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.table tbody tr:hover {
    background-color: var(--md-grey-50);
    transform: scale(1.01);
}

/* Modern Badges */
.badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--status-active) 0%, #388e3c 100%);
    color: var(--md-white);
    border-color: var(--status-active);
}

/* Modern Attachments */
.attachment-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    margin-bottom: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    background: var(--surface-color);
}

.attachment-item:hover {
    background-color: var(--md-grey-50);
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.attachment-icon {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--md-white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-size: 14px;
}

.attachment-meta {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.attachment-actions {
    display: flex;
    gap: 8px;
}

.attachment-actions .btn {
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.attachment-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

/* Modern Responsive Design */
@media (max-width: 768px) {
    .part-header {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 12px;
    }

    .part-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .part-number {
        font-size: 24px;
    }

    .part-description {
        font-size: 18px;
    }

    .part-meta {
        gap: 12px;
        flex-direction: column;
        align-items: flex-start;
    }

    .meta-item {
        width: 100%;
        justify-content: space-between;
    }

    .part-actions {
        justify-content: center;
        margin-top: 16px;
        width: 100%;
        gap: 8px;
    }

    .part-actions .btn {
        flex: 1;
        max-width: 120px;
    }

    .detail-card .card-header {
        padding: 16px 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .detail-card .card-body {
        padding: 20px;
    }

    .nav-tabs {
        padding: 4px;
        gap: 2px;
    }

    .nav-tabs .nav-link {
        padding: 10px 16px;
        font-size: 13px;
        border-radius: 8px;
    }

    .nav-tabs .nav-link .material-icons {
        font-size: 16px;
    }

    .tab-pane {
        padding: 16px 0;
    }

    .attachment-item {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .attachment-icon {
        width: 48px;
        height: 48px;
        margin-right: 0;
        margin-bottom: 8px;
    }

    .attachment-actions {
        justify-content: center;
        width: 100%;
    }

    .info-group {
        margin-bottom: 16px;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .header-actions .btn {
        flex: 1;
        margin: 0 4px;
    }

    /* Mobile Image Gallery Styles */
    .main-image-container {
        margin: 16px;
    }

    .main-image-wrapper {
        aspect-ratio: 16/9;
    }

    .image-actions .btn {
        width: 36px;
        height: 36px;
    }

    .image-actions .btn .material-icons {
        font-size: 18px;
    }

    .thumbnail-scroll {
        gap: 8px;
    }

    .thumbnail-item {
        width: 70px;
    }

    .thumbnail-item img {
        height: 50px;
    }

    .thumbnail-label {
        font-size: 9px;
        padding: 4px 6px;
    }

    .view-toggle-buttons {
        gap: 6px;
    }

    .view-btn {
        padding: 8px 12px;
        min-width: 60px;
    }

    .view-btn .material-icons {
        font-size: 16px;
    }

    .view-btn span {
        font-size: 10px;
    }

    .image-view-options {
        padding: 12px 16px;
    }

    .upload-zone {
        padding: 30px 15px;
    }

    .upload-icon .material-icons {
        font-size: 36px;
    }

    .zoom-controls {
        bottom: 10px;
        padding: 8px;
    }

    .zoom-controls .btn {
        width: 36px;
        height: 36px;
    }

    /* Modern Layout Mobile Styles */
    .part-header-modern {
        border-radius: 16px;
        margin-bottom: 24px;
    }

    .header-content {
        padding: 20px;
    }

    .part-number {
        font-size: 24px;
    }

    .part-description {
        font-size: 18px;
    }

    .part-meta-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .meta-card {
        padding: 12px;
    }

    .meta-icon {
        width: 36px;
        height: 36px;
        margin-right: 10px;
    }

    .part-actions-modern {
        gap: 16px;
        margin-top: 20px;
    }

    .action-buttons .btn {
        padding: 10px 16px;
    }

    .action-buttons .btn-lg {
        padding: 12px 20px;
        font-size: 14px;
    }

    .quick-stats {
        flex-direction: row;
        gap: 8px;
    }

    .stat-card {
        flex: 1;
        padding: 12px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }

    .detail-tabs-modern {
        border-radius: 16px;
        margin-bottom: 24px;
    }

    .tabs-container {
        padding: 16px;
    }

    .tabs-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 16px;
    }

    .nav-modern {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .nav-modern .nav-link {
        padding: 12px;
    }

    .nav-icon {
        width: 36px;
        height: 36px;
        margin-right: 10px;
    }

    .detail-card-modern {
        border-radius: 16px;
    }

    .card-header-modern {
        padding: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .header-icon {
        width: 44px;
        height: 44px;
        margin-right: 0;
        margin-bottom: 8px;
    }

    .card-body-modern {
        padding: 16px;
    }

    .info-grid {
        gap: 24px;
    }

    .info-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .info-item.full-width {
        grid-column: 1;
    }

    .info-value {
        padding: 10px 12px;
        min-height: 40px;
    }

    /* Compact Layout Mobile Styles */
    .part-header-compact {
        border-radius: 12px;
        margin-bottom: 16px;
    }

    .header-content-compact {
        padding: 16px;
    }

    .title-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .part-title-compact {
        font-size: 20px;
    }

    .part-description-compact {
        font-size: 14px;
    }

    .action-buttons-compact {
        width: 100%;
        justify-content: flex-end;
    }

    .meta-row {
        flex-direction: column;
        gap: 8px;
    }

    .meta-item-compact {
        font-size: 11px;
        padding: 6px 8px;
    }

    .stats-row {
        gap: 8px;
    }

    .stat-compact {
        font-size: 11px;
        padding: 8px 10px;
    }

    .detail-tabs-compact {
        padding: 12px;
        margin-bottom: 16px;
    }

   .tabs-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--md-grey-200);
}

    .nav-compact {
        gap: 6px;
    }

    .nav-compact .nav-link {
        padding: 6px 8px;
        font-size: 11px;
        min-height: 32px;
    }

    .detail-card-compact {
        margin-bottom: 12px;
    }

    .card-header-compact {
        padding: 10px 12px;
    }

    .card-body-compact {
        padding: 12px;
    }

    .info-row-compact {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .info-item-compact.full-width {
        grid-column: 1;
    }

    .info-value-compact {
        padding: 4px 6px;
        min-height: 24px;
        font-size: 11px;
    }

    .main-part-image-compact {
        height: 150px;
    }

    .thumbnails-compact {
        gap: 4px;
    }

    .thumbnail-item-compact {
        width: 40px;
        height: 28px;
    }

    .btn-xs {
        min-width: 24px;
        height: 24px;
        padding: 2px 4px;
    }

    .btn-xs .material-icons {
        font-size: 12px;
    }

    /* Ultra Compact Layout Mobile Styles */
    .quick-stats-row {
        flex-direction: column;
        gap: 6px;
        padding: 8px;
    }

    .stat-item-ultra-compact {
        padding: 6px 8px;
        min-height: 36px;
        gap: 6px;
    }

    .stat-item-ultra-compact .material-icons {
        font-size: 16px;
    }

    .stat-value {
        font-size: 12px;
    }

    .stat-label {
        font-size: 9px;
    }

    .detail-card-ultra-compact {
        margin-bottom: 8px;
    }

    .card-header-ultra-compact {
        padding: 8px 10px;
    }

    .card-header-ultra-compact h6 {
        font-size: 12px;
    }

    .card-body-ultra-compact {
        padding: 10px;
    }

    .info-row-ultra-compact {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .info-item-ultra-compact.full-width {
        grid-column: 1;
    }

    .info-item-ultra-compact label {
        font-size: 8px;
        margin-bottom: 2px;
    }

    .info-value-ultra-compact {
        padding: 3px 5px;
        min-height: 20px;
        font-size: 10px;
    }

    .main-part-image-ultra-compact {
        height: 240px;
    }

    .thumbnails-ultra-compact {
        gap: 3px;
        padding: 4px 0;
    }

    .thumbnail-item-ultra-compact {
        width: 36px;
        height: 26px;
    }

    .btn-xxs {
        min-width: 20px;
        height: 20px;
        padding: 2px 3px;
    }

    .btn-xxs .material-icons {
        font-size: 10px;
    }

    .additional-info-toggle-ultra-compact {
        margin-top: 4px;
        padding-top: 4px;
    }

    .additional-info-toggle-ultra-compact .btn-link {
        font-size: 10px;
    }
}

/* Extra small devices */
@media (max-width: 576px) {
    .part-header {
        padding: 16px;
        margin-bottom: 16px;
    }

    .part-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .part-number {
        font-size: 20px;
    }

    .part-description {
        font-size: 16px;
    }

    .detail-card .card-header {
        padding: 12px 16px;
    }

    .detail-card .card-body {
        padding: 16px;
    }

    .nav-tabs {
        flex-direction: column;
        padding: 2px;
    }

    .nav-tabs .nav-link {
        text-align: left;
        border-bottom: 1px solid var(--md-grey-200);
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 6px;
        margin-bottom: 2px;
    }

    .nav-tabs .nav-link.active {
        border-bottom: 1px solid var(--primary-color);
        border-left: 3px solid var(--primary-color);
    }

    .nav-tabs .nav-link .material-icons {
        font-size: 16px;
    }

    .table-responsive {
        font-size: 12px;
    }

    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px 0;
    }

    .info-group {
        margin-bottom: 12px;
    }

    .header-actions .btn {
        font-size: 11px;
        padding: 6px 12px;
    }
}

/* Compact Part Header - Material Design 3 */
.part-header-compact {
    position: relative;
    background: linear-gradient(135deg, var(--md-white) 0%, var(--md-grey-50) 100%);
    border-radius: 16px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.03);
    border: 1px solid var(--md-grey-200);
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--primary-color) 0%, #424242 50%, var(--primary-color) 100%);
}

.header-content-compact {
    padding: 20px;
}

.compact-breadcrumb {
    margin-bottom: 8px;
}

.compact-breadcrumb .breadcrumb {
    font-size: 12px;
    margin: 0;
}

.part-title-compact {
    margin-bottom: 16px;
}

.title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.part-title-compact {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.part-description-compact {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.action-buttons-compact {
    display: flex;
    gap: 6px;
}

.action-buttons-compact .btn {
    border-radius: 8px;
    padding: 6px 8px;
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.meta-stats-compact {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.meta-row {
    display: flex;
    gap: 16px;
}

.meta-item-compact {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    padding: 8px 12px;
    background: var(--md-white);
    border-radius: 8px;
    border: 1px solid var(--md-grey-200);
    font-size: 12px;
}

.meta-item-compact .material-icons {
    font-size: 16px;
    color: var(--primary-color);
}

.meta-item-compact .meta-label {
    font-weight: 600;
    color: var(--text-secondary);
}

.meta-item-compact .meta-value {
    font-weight: 600;
    color: var(--text-primary);
}

.stats-row {
    display: flex;
    gap: 12px;
}

.stat-compact {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    padding: 10px 12px;
    background: var(--md-white);
    border-radius: 8px;
    border: 1px solid var(--md-grey-200);
    font-size: 12px;
}

.stat-compact .material-icons {
    font-size: 18px;
}

.stat-compact .stat-value {
    font-weight: 700;
    color: var(--text-primary);
}

.stat-compact .stat-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.part-breadcrumb {
    margin-bottom: 16px;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 500;
}

.part-title-wrapper {
    margin-bottom: 24px;
}

.part-title {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.part-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Roboto Mono', monospace;
    letter-spacing: 0.5px;
}

.part-description {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.part-meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.meta-card {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--md-white);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.meta-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.meta-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.meta-icon .material-icons {
    color: var(--md-white);
    font-size: 20px;
}

.meta-content {
    flex: 1;
}

.meta-label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.75px;
    margin-bottom: 4px;
}

.meta-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.part-actions-modern {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-buttons .btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 20px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    border-width: 2px;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.action-buttons .btn-lg {
    padding: 16px 24px;
    font-size: 16px;
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--md-white);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stat-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--md-white);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
}

.stat-icon.bg-success {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.stat-content {
    flex: 1;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.stat-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Compact Navigation Tabs */
.detail-tabs-compact {
    background: var(--md-white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.03);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid var(--md-grey-200);
    padding: 16px;
}

.tabs-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--md-grey-200);
}

.tabs-title-compact {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.nav-compact {
    display: flex;
    gap: 8px;
    border: none;
    flex-wrap: wrap;
}

.nav-compact .nav-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid var(--md-grey-200);
    border-radius: 8px;
    background: var(--md-white);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    font-size: 12px;
    font-weight: 600;
    min-height: 36px;
}

.nav-compact .nav-link:hover {
    border-color: var(--primary-color);
    background: var(--md-grey-50);
    color: var(--primary-color);
}

.nav-compact .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.nav-compact .nav-link .material-icons {
    font-size: 16px;
}

.tabs-container {
    padding: 24px;
}

.tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--md-grey-200);
}

.tabs-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.tabs-actions .btn {
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 8px 16px;
}

.nav-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
    border: none;
}

.nav-modern .nav-link {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid var(--md-grey-200);
    border-radius: 12px;
    background: var(--md-white);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-modern .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.01) 100%);
    opacity: 0;
    transition: opacity 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.nav-modern .nav-link:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.nav-modern .nav-link:hover::before {
    opacity: 1;
}

.nav-modern .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    border-color: var(--primary-color);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.nav-icon {
    width: 44px;
    height: 44px;
    border-radius: 10px;
    background: var(--md-grey-100);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.nav-modern .nav-link.active .nav-icon {
    background: rgba(255, 255, 255, 0.2);
}

.nav-icon .material-icons {
    font-size: 20px;
    color: var(--text-secondary);
}

.nav-modern .nav-link.active .nav-icon .material-icons {
    color: var(--md-white);
}

.nav-content {
    flex: 1;
}

.nav-title {
    display: block;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.nav-subtitle {
    display: block;
    font-size: 11px;
    font-weight: 500;
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Compact Detail Cards */
.detail-card-compact {
    background: var(--md-white);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.03);
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    border: 1px solid var(--md-grey-200);
}

.detail-card-compact:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
}

.card-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
}

.card-header-compact h6 {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-header-compact .material-icons {
    color: var(--primary-color);
    font-size: 16px;
}

.header-actions-compact {
    display: flex;
    gap: 8px;
    align-items: center;
}
.header-actions-compact .btn {
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 8px 16px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}
.header-actions-compact .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}
.header-actions-compact .btn .material-icons {
    font-size: 16px;
    margin-right: 6px;
}
.btn-xs {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 6px;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-xs .material-icons {
    font-size: 14px;
}

.card-body-compact {
    padding: 16px;
}

/* Compact Information Grid */
.info-grid-compact {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-row-compact {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    align-items: start;
}

.info-item-compact {
    position: relative;
}

.info-item-compact.full-width {
    grid-column: 1 / -1;
}

.info-item-compact label {
    display: block;
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value-compact {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.3;
    padding: 6px 8px;
    background: var(--md-grey-50);
    border-radius: 6px;
    border: 1px solid var(--md-grey-200);
    min-height: 28px;
    display: flex;
    align-items: center;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.info-value-compact.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    font-weight: 600;
    border-color: var(--primary-color);
}

.info-value-compact:hover {
    border-color: var(--primary-color);
    background: var(--md-white);
}

.info-value-compact.badge-value .badge {
    font-size: 9px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.additional-info-toggle {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--md-grey-200);
}

.additional-info-toggle .btn-link {
    color: var(--primary-color);
    font-size: 12px;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.additional-info-toggle .btn-link:hover {
    color: #424242;
}

.additional-info-toggle .material-icons {
    transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.additional-info-toggle .btn-link[aria-expanded="true"] .material-icons {
    transform: rotate(180deg);
}

.detail-card-modern:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.card-header-modern {
    display: flex;
    align-items: center;
    padding: 24px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    position: relative;
}

.card-header-modern::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
}

.header-icon {
    width: 52px;
    height: 52px;
    border-radius: 14px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--md-white);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-content {
    flex: 1;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    letter-spacing: 0.25px;
}

.header-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

.card-body-modern {
    padding: 24px;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.info-section {
    position: relative;
}

.section-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--md-grey-100);
    text-transform: uppercase;
    letter-spacing: 0.75px;
}

.section-title .material-icons {
    color: var(--primary-color);
    font-size: 18px;
}

.info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 16px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-item {
    position: relative;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.75px;
}

.required {
    color: #f44336;
    font-weight: 700;
}

.info-value {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.4;
    padding: 12px 16px;
    background: var(--md-grey-50);
    border-radius: 8px;
    border: 1px solid var(--md-grey-200);
    min-height: 44px;
    display: flex;
    align-items: center;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.info-value.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    font-weight: 600;
    border-color: var(--primary-color);
}

.info-value:hover {
    border-color: var(--primary-color);
    background: var(--md-white);
}

.info-value.badge-value .badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Ultra Compact Quick Stats Row */
.quick-stats-row {
    display: flex;
    gap: 8px;
    padding: 12px;
    background: linear-gradient(135deg, var(--md-white) 0%, var(--md-grey-50) 100%);
    border-radius: 10px;
    border: 1px solid var(--md-grey-200);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stat-item-ultra-compact {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    padding: 8px 10px;
    background: var(--md-white);
    border-radius: 8px;
    border: 1px solid var(--md-grey-200);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    min-height: 44px;
}

.stat-item-ultra-compact:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.stat-item-ultra-compact .material-icons {
    font-size: 18px;
    flex-shrink: 0;
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.stat-value {
    font-size: 14px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

/* Ultra Compact Detail Cards */
.detail-card-ultra-compact {
    background: var(--md-white);
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    border: 1px solid var(--md-grey-200);
}

.detail-card-ultra-compact:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.03);
    transform: translateY(-1px);
}

.card-header-ultra-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
}

.card-header-ultra-compact h6 {
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-header-ultra-compact .material-icons {
    color:white;
    font-size: 14px;
}

.header-actions-ultra-compact {
    display: flex;
    gap: 3px;
}

.btn-xxs {
    padding: 3px 6px;
    font-size: 10px;
    border-radius: 5px;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-xxs .material-icons {
    font-size: 12px;
}

.card-body-ultra-compact {
    padding: 12px;
}

/* Ultra Compact Information Grid */
.info-grid-ultra-compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-row-ultra-compact {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    align-items: start;
}

.info-item-ultra-compact {
    position: relative;
}

.info-item-ultra-compact.full-width {
    grid-column: 1 / -1;
}

.info-item-ultra-compact label {
    display: block;
    font-size: 9px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 3px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.info-value-ultra-compact {
    font-size: 11px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.2;
    padding: 4px 6px;
    background: var(--md-grey-50);
    border-radius: 5px;
    border: 1px solid var(--md-grey-200);
    min-height: 24px;
    display: flex;
    align-items: center;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.info-value-ultra-compact.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    font-weight: 600;
    border-color: var(--primary-color);
}

.info-value-ultra-compact:hover {
    border-color: var(--primary-color);
    background: var(--md-white);
}

.info-value-ultra-compact.badge-value .badge {
    font-size: 8px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.2px;
}

.additional-info-toggle-ultra-compact {
    margin-top: 6px;
    padding-top: 6px;
    border-top: 1px solid var(--md-grey-200);
}

.additional-info-toggle-ultra-compact .btn-link {
    color: var(--primary-color);
    font-size: 11px;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.additional-info-toggle-ultra-compact .btn-link:hover {
    color: #424242;
}

.additional-info-toggle-ultra-compact .material-icons {
    transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    font-size: 14px;
}

.additional-info-toggle-ultra-compact .btn-link[aria-expanded="true"] .material-icons {
    transform: rotate(180deg);
}

/* Ultra Compact Part Images Gallery */
.part-images-ultra-compact {
    position: relative;
    overflow: hidden;
}

.main-image-ultra-compact {
    position: relative;
    background: var(--md-grey-50);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 6px;
}

.main-part-image-ultra-compact {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: zoom-in;
}

.main-part-image-ultra-compact:hover {
    transform: scale(1.02);
}

.main-part-image-ultra-compact.loaded {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Image Controls Overlay */
.image-controls-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.main-image-ultra-compact:hover .image-controls-overlay {
    opacity: 1;
}

.image-controls-overlay .btn {
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-controls-overlay .btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.05);
}

.image-overlay-ultra-compact {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0.03) 100%);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-image-ultra-compact:hover .image-overlay-ultra-compact {
    opacity: 1;
}

.image-actions-ultra-compact {
    display: flex;
    gap: 3px;
}

.image-actions-ultra-compact .btn {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(6px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.image-actions-ultra-compact .btn .material-icons {
    font-size: 12px;
}

.thumbnails-ultra-compact {
    display: flex;
    gap: 4px;
    padding: 6px 0;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--md-grey-400) var(--md-grey-100);
    min-height: 32px;
}

.thumbnails-ultra-compact.loading {
    justify-content: center;
    align-items: center;
}

.thumbnails-loading {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-secondary);
    font-size: 11px;
    font-weight: 500;
}

.thumbnails-loading .material-icons {
    font-size: 14px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.thumbnails-ultra-compact::-webkit-scrollbar {
    height: 3px;
}

.thumbnails-ultra-compact::-webkit-scrollbar-track {
    background: var(--md-grey-100);
    border-radius: 2px;
}

.thumbnails-ultra-compact::-webkit-scrollbar-thumb {
    background: var(--md-grey-400);
    border-radius: 2px;
}

.thumbnail-item-ultra-compact {
    flex-shrink: 0;
    width: 60px;
    height: 45px;
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    background: var(--md-white);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    position: relative;
    opacity: 0;
    transform: translateY(10px);
    animation: fadeInUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.thumbnail-item-ultra-compact:hover {
    border-color: var(--md-grey-400);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.thumbnail-item-ultra-compact.active {
    border-color: var(--primary-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.thumbnail-item-ultra-compact img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.thumbnail-item-ultra-compact::after {
    content: attr(data-view);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: white;
    font-size: 8px;
    font-weight: 600;
    text-transform: capitalize;
    letter-spacing: 0.2px;
    padding: 3px 4px;
    text-align: center;
    opacity: 0;
    transition: opacity 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.thumbnail-item-ultra-compact:hover::after {
    opacity: 1;
}

.thumbnail-item-ultra-compact.clicked {
    transform: scale(0.95);
    transition: transform 0.1s ease-in-out;
}

/* No Images Placeholder */
.no-images-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
}

.no-images-placeholder .material-icons {
    font-size: 24px;
    margin-bottom: 8px;
    opacity: 0.5;
}

/* Image View Options for Ultra Compact */
.image-view-options {
    padding: 8px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
    display: flex;
    justify-content: center;
}

.image-view-options .btn-group {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.image-view-options .view-btn {
    padding: 6px 8px;
    font-size: 12px;
    border: none;
    background: var(--md-white);
    color: var(--text-secondary);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.image-view-options .view-btn:hover {
    background: var(--md-grey-100);
    color: var(--primary-color);
}

.image-view-options .view-btn.active {
    background: var(--primary-color);
    color: var(--md-white);
}

.image-view-options .view-btn .material-icons {
    font-size: 16px;
}

/* Compact Part Images Gallery */
.part-images-compact {
    position: relative;
    overflow: hidden;
}

.main-image-compact {
    position: relative;
    background: var(--md-grey-50);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.main-part-image-compact {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: zoom-in;
}

.main-part-image-compact:hover {
    transform: scale(1.02);
}

.image-overlay-compact {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-image-compact:hover .image-overlay-compact {
    opacity: 1;
}

.image-actions-compact {
    display: flex;
    gap: 4px;
}

.image-actions-compact .btn {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-actions-compact .btn .material-icons {
    font-size: 14px;
}

.thumbnails-compact {
    display: flex;
    gap: 6px;
    padding: 8px 0;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--md-grey-400) var(--md-grey-100);
}

.thumbnails-compact::-webkit-scrollbar {
    height: 4px;
}

.thumbnails-compact::-webkit-scrollbar-track {
    background: var(--md-grey-100);
    border-radius: 2px;
}

.thumbnails-compact::-webkit-scrollbar-thumb {
    background: var(--md-grey-400);
    border-radius: 2px;
}

.thumbnail-item-compact {
    flex-shrink: 0;
    width: 50px;
    height: 35px;
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    background: var(--md-white);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.thumbnail-item-compact:hover {
    border-color: var(--md-grey-400);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail-item-compact.active {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.thumbnail-item-compact img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Modern Part Images Gallery - Material Design 3 */
.part-images-card {
    position: relative;
    overflow: hidden;
}

.main-image-container {
    position: relative;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-radius: 12px;
    margin: 20px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
}

.main-image-wrapper {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    background: var(--md-white);
}

.main-part-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: zoom-in;
}

.main-part-image:hover {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-image-wrapper:hover .image-overlay {
    opacity: 1;
}

.image-actions {
    display: flex;
    gap: 8px;
    transform: translateY(20px);
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.main-image-wrapper:hover .image-actions {
    transform: translateY(0);
}

.image-actions .btn {
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.image-actions .btn:hover {
    background: var(--md-white);
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.image-info {
    padding: 16px 20px;
    background: var(--md-white);
    border-top: 1px solid var(--md-grey-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.image-meta {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Image Thumbnails */
.image-thumbnails {
    padding: 16px 20px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
}

.thumbnail-scroll {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 4px 0;
    scrollbar-width: thin;
    scrollbar-color: var(--md-grey-400) var(--md-grey-100);
}

.thumbnail-scroll::-webkit-scrollbar {
    height: 6px;
}

.thumbnail-scroll::-webkit-scrollbar-track {
    background: var(--md-grey-100);
    border-radius: 3px;
}

.thumbnail-scroll::-webkit-scrollbar-thumb {
    background: var(--md-grey-400);
    border-radius: 3px;
}

.thumbnail-scroll::-webkit-scrollbar-thumb:hover {
    background: var(--md-grey-600);
}

.thumbnail-item {
    flex-shrink: 0;
    width: 80px;
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    background: var(--md-white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.thumbnail-item:hover {
    border-color: var(--md-grey-400);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.thumbnail-item.active {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.thumbnail-item img {
    width: 100%;
    height: 60px;
    object-fit: cover;
    display: block;
}

.thumbnail-label {
    display: block;
    padding: 6px 8px;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
}

.thumbnail-item.active .thumbnail-label {
    color: var(--primary-color);
    background: var(--md-grey-100);
}

/* Image View Options */
.image-view-options {
    padding: 16px 20px;
    background: var(--md-white);
    border-top: 1px solid var(--md-grey-200);
}

.view-toggle-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.view-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    border: 1px solid var(--md-grey-300);
    border-radius: 8px;
    background: var(--md-white);
    color: var(--text-secondary);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    min-width: 70px;
}

.view-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--md-grey-50);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.view-btn.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.view-btn .material-icons {
    font-size: 18px;
}

.view-btn span {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Upload Modal Styles */
.upload-area {
    margin-bottom: 20px;
}

.upload-zone {
    border: 2px dashed var(--md-grey-300);
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: var(--md-grey-50);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
}

.upload-zone:hover {
    border-color: var(--primary-color);
    background: var(--md-grey-100);
}

.upload-zone.dragover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
    transform: scale(1.02);
}

.upload-icon {
    margin-bottom: 16px;
}

.upload-icon .material-icons {
    font-size: 48px;
    color: var(--md-grey-500);
}

.upload-zone h6 {
    color: var(--text-primary);
    margin-bottom: 8px;
    font-weight: 600;
}

.upload-zone p {
    margin-bottom: 20px;
    font-size: 14px;
}

.preview-images {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.preview-item {
    position: relative;
    width: 120px;
    height: 90px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--md-grey-200);
    background: var(--md-white);
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(244, 67, 54, 0.9);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.preview-remove:hover {
    background: #d32f2f;
    transform: scale(1.1);
}

/* Zoom Modal Styles */
.zoom-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoom-image {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: grab;
}

.zoom-image:active {
    cursor: grabbing;
}

.zoom-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    background: rgba(0, 0, 0, 0.7);
    padding: 12px;
    border-radius: 24px;
    backdrop-filter: blur(10px);
}

.zoom-controls .btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.zoom-controls .btn:hover {
    background: var(--md-white);
    transform: scale(1.1);
}

/* Print Styles */
@media print {
    .navbar,
    .part-actions,
    .detail-tabs .nav-tabs,
    .image-overlay,
    .image-actions,
    .view-toggle-buttons {
        display: none !important;
    }

    .main-content {
        margin-top: 0 !important;
    }

    .detail-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .tab-content .tab-pane {
        display: block !important;
    }

    .main-part-image {
        max-height: 300px;
        width: auto;
    }

    .image-thumbnails {
        display: none;
    }
}

/* Compare Viewer Styles */
.compare-viewer {
    position: relative;
    width: 100%;
    background: var(--md-white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.compare-viewer.active {
    opacity: 1;
    transform: translateY(0);
}

.compare-container {
    padding: 16px;
}

.compare-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--md-grey-300);
}

.compare-header h6 {
    margin: 0;
    color: var(--md-grey-800);
    font-weight: 500;
    display: flex;
    align-items: center;
}

.compare-controls {
    display: flex;
    gap: 8px;
}

.compare-controls .btn {
    padding: 4px 8px;
    border-radius: 4px;
}

.compare-images {
    display: flex;
    gap: 16px;
    align-items: stretch;
    margin-bottom: 16px;
}

.compare-image-slot {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.compare-image-container {
    position: relative;
    background: var(--md-grey-100);
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 4/3;
    border: 2px solid var(--md-grey-300);
    transition: border-color 0.2s ease;
}

.compare-image-container:hover {
    border-color: var(--primary-color);
}

.compare-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    cursor: grab;
}

.compare-image:active {
    cursor: grabbing;
}

.compare-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.7) 0%,
        transparent 30%,
        transparent 70%,
        rgba(0, 0, 0, 0.7) 100%
    );
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 12px;
}

.compare-image-container:hover .compare-image-overlay {
    opacity: 1;
}

.compare-image-label {
    color: white;
    font-weight: 500;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.compare-image-controls {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.compare-image-controls .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 4px;
}

.compare-image-controls .btn:hover {
    background: white;
    transform: scale(1.05);
}

.compare-image-controls .material-icons {
    font-size: 16px;
}

.compare-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 40px;
    position: relative;
}

.compare-divider-line {
    width: 2px;
    height: 100%;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        var(--md-grey-400) 20%,
        var(--md-grey-400) 80%,
        transparent 100%
    );
}

.compare-vs {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--primary-color);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.compare-image-selector select {
    border-radius: 6px;
    border: 1px solid var(--md-grey-300);
    font-size: 12px;
    padding: 6px 8px;
}

.compare-image-selector select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.compare-info {
    display: flex;
    gap: 24px;
    padding: 12px 16px;
    background: var(--md-grey-50);
    border-radius: 6px;
    border: 1px solid var(--md-grey-200);
}

.compare-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.compare-info-label {
    font-size: 12px;
    color: var(--md-grey-600);
    font-weight: 500;
}

.compare-info-value {
    font-size: 12px;
    color: var(--md-grey-800);
    font-weight: 600;
    padding: 2px 6px;
    background: white;
    border-radius: 3px;
    border: 1px solid var(--md-grey-300);
}

/* Empty state for compare images */
.compare-image-container.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--md-grey-50);
    border-style: dashed;
}

.compare-image-container.empty::before {
    content: "Select an image";
    color: var(--md-grey-500);
    font-size: 14px;
    text-align: center;
}

/* Responsive design for compare viewer */
@media (max-width: 768px) {
    .compare-images {
        flex-direction: column;
        gap: 12px;
    }

    .compare-divider {
        width: 100%;
        height: 40px;
        flex-direction: row;
    }

    .compare-divider-line {
        width: 100%;
        height: 2px;
    }

    .compare-info {
        flex-direction: column;
        gap: 12px;
    }
    .header-actions-compact {
        gap: 4px;
    }

    .header-actions-compact .btn {
        padding: 4px 8px;
        font-size: 10px;
    }

    .header-actions-compact .btn .material-icons {
        font-size: 12px;
        margin-right: 2px;
    }
}




/* Editable Input Fields */
.info-value-ultra-compact input,
.info-value-ultra-compact select {
    width: 100%;
    font-size: 11px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.2;
    padding: 4px 6px;
    background: var(--md-white);
    border-radius: 5px;
    border: 1px solid var(--md-grey-300);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    min-height: 24px;
}

.info-value-ultra-compact input:focus,
.info-value-ultra-compact select:focus {
    border-color: var(--primary-color);
    background: var(--md-white);
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.info-value-ultra-compact select {
    appearance: none;
    background-image: url("data:image/svg+xml;utf8,<svg fill='var(--text-secondary)' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
    background-repeat: no-repeat;
    background-position: right 6px top 50%;
    padding-right: 24px;
}

/* Ensure Save and Cancel buttons align with edit button */
.header-actions-ultra-compact .btn-save,
.header-actions-ultra-compact .btn-cancel {
    padding: 3px 6px;
    font-size: 10px;
    border-radius: 5px;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-actions-ultra-compact .btn-save .material-icons,
.header-actions-ultra-compact .btn-cancel .material-icons {
    font-size: 12px;
}




/* stock */

/* Stock Details Tab - Material Design 3 Enhanced Styles */

/* Card Header */
#stock .detail-card {
    border-radius: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15); /* Elevation 1 */
    background: var(--md-sys-color-surface, #ffffff);
}

#stock .detail-card .card-header {
    background: var(--md-sys-color-surface, #ffffff);
    border-bottom: 1px solid var(--md-sys-color-outline-variant, #e0e0e0);
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

#stock .detail-card .card-header h5 {
    margin: 0;
    color: var(--md-sys-color-on-surface, #1c2526);
    font-family: 'Roboto', sans-serif;
    font-size: 20px; /* Material Design 3 Headline Small */
    font-weight: 500;
    line-height: 28px;
    letter-spacing: 0.15px;
    display: flex;
    align-items: center;
}

#stock .detail-card .card-header .material-icons {
    color: var(--md-sys-color-primary, #1976d2);
    font-size: 24px;
    margin-right: 8px;
}

#stock .detail-card .card-body {
    padding: 24px;
    background: var(--md-sys-color-surface, #ffffff);
}

/* Search Bar */
#stock .input-group-sm {
    max-width: 280px;
    border-radius: 28px; /* Material Design 3 rounded shape */
    overflow: hidden;
    background: var(--md-sys-color-surface-container-highest, #f5f5f5);
    border: 1px solid var(--md-sys-color-outline, #757575);
    transition: border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .input-group-sm .input-group-text {
    background: transparent;
    border: none;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    padding: 8px 12px;
}

#stock .input-group-sm .form-control {
    font-family: 'Roboto', sans-serif;
    font-size: 14px; /* Material Design 3 Body Medium */
    font-weight: 400;
    line-height: 20px;
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: var(--md-sys-color-on-surface, #1c2526);
}

#stock .input-group-sm .form-control:focus {
    outline: none;
    box-shadow: none;
}

#stock .input-group-sm:focus-within {
    border-color: var(--md-sys-color-primary, #1976d2);
    box-shadow: 0 0 0 2px var(--md-sys-color-primary-container, rgba(25, 118, 210, 0.2));
}

/* Filter Dropdown */
#stock .dropdown .btn-sm {
    border-radius: 28px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.1px;
    padding: 8px 16px;
    border: 1px solid var(--md-sys-color-outline, #757575);
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    background: var(--md-sys-color-surface-container-low, #f9f9f9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .dropdown .btn-sm:hover {
    background: var(--md-sys-color-surface-container, #f0f0f0);
    border-color: var(--md-sys-color-primary, #1976d2);
    color: var(--md-sys-color-primary, #1976d2);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15); /* Elevation 1 */
}

#stock .dropdown .btn-sm .material-icons {
    font-size: 20px;
    vertical-align: middle;
    margin-right: 4px;
}

#stock .dropdown-menu {
    border-radius: 12px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.15); /* Elevation 3 */
    border: none;
    padding: 16px;
    min-width: 320px;
    background: var(--md-sys-color-surface, #ffffff);
}

#stock .dropdown-menu .form-label {
    font-family: 'Roboto', sans-serif;
    font-size: 12px; /* Material Design 3 Label Small */
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0.5px;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    margin-bottom: 8px;
    text-transform: uppercase;
}

#stock .dropdown-menu .form-select-sm,
#stock .dropdown-menu .form-control-sm {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid var(--md-sys-color-outline, #757575);
    background: var(--md-sys-color-surface-container-low, #f9f9f9);
    color: var(--md-sys-color-on-surface, #1c2526);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .dropdown-menu .form-select-sm:focus,
#stock .dropdown-menu .form-control-sm:focus {
    border-color: var(--md-sys-color-primary, #1976d2);
    box-shadow: 0 0 0 2px var(--md-sys-color-primary-container, rgba(25, 118, 210, 0.2));
    outline: none;
}

#stock .dropdown-menu .btn-sm {
    border-radius: 28px;
    padding: 8px 16px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.1px;
    background: var(--md-sys-color-primary, #1976d2);
    border: none;
    color: var(--md-sys-color-on-primary, #ffffff);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .dropdown-menu .btn-sm:hover {
    background: var(--md-sys-color-primary-container, #90caf9);
    color: var(--md-sys-color-on-primary-container, #0d47a1);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

/* Add Stock Entry and Delete Buttons */
#stock .btn-primary.btn-sm,
#stock .btn-danger.btn-sm {
    border-radius: 28px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.1px;
    padding: 8px 16px;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .btn-primary.btn-sm {
    background: var(--md-sys-color-primary, #1976d2);
    color: var(--md-sys-color-on-primary, #ffffff);
}

#stock .btn-primary.btn-sm:hover {
    background: var(--md-sys-color-primary-container, #90caf9);
    color: var(--md-sys-color-on-primary-container, #0d47a1);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

#stock .btn-danger.btn-sm {
    background: var(--md-sys-color-error, #d32f2f);
    color: var(--md-sys-color-on-error, #ffffff);
}

#stock .btn-danger.btn-sm:hover {
    background: var(--md-sys-color-error-container, #ef5350);
    color: var(--md-sys-color-on-error-container, #b71c1c);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

#stock .btn-primary.btn-sm .material-icons,
#stock .btn-danger.btn-sm .material-icons {
    font-size: 20px;
    vertical-align: middle;
    margin-right: 4px;
}

/* Bulk Actions */
#stock #bulkActions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

#stock #bulkActions .btn-danger {
    border-radius: 28px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.1px;
    padding: 8px 16px;
    background: var(--md-sys-color-error, #d32f2f);
    border: none;
    color: var(--md-sys-color-on-error, #ffffff);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock #bulkActions .btn-danger:hover {
    background: var(--md-sys-color-error-container, #ef5350);
    color: var(--md-sys-color-on-error-container, #b71c1c);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

/* Loading Spinner */
#stock #stockLoading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 24px 0;
}

#stock #stockLoading .spinner-border {
    width: 32px;
    height: 32px;
    border-width: 4px;
    color: var(--md-sys-color-primary, #1976d2);
}

/* Stock Table (Grid View) */
#stock .table-responsive {
    max-height: 500px;
    overflow-y: auto;
    border-radius: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15); /* Elevation 1 */
    border: none;
    background: var(--md-sys-color-surface, #ffffff);
}

#stock .table {
    margin-bottom: 0;
}

#stock .table thead th {
    background: var(--md-sys-color-surface-container-low, #f9f9f9);
    border-bottom: 1px solid var(--md-sys-color-outline-variant, #e0e0e0);
    font-family: 'Roboto', sans-serif;
    font-size: 12px; /* Material Design 3 Label Medium */
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0.5px;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    padding: 12px 16px;
    text-transform: uppercase;
    position: sticky;
    top: 0;
    z-index: 1;
}

#stock .table tbody td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--md-sys-color-outline-variant, #e0e0e0);
    vertical-align: middle;
    font-family: 'Roboto', sans-serif;
    font-size: 14px; /* Material Design 3 Body Medium */
    font-weight: 400;
    line-height: 20px;
    color: var(--md-sys-color-on-surface, #1c2526);
}

#stock .table tbody tr {
    transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .table tbody tr:hover {
    background: var(--md-sys-color-surface-container, #f0f0f0);
}

/* Sortable Columns */
#stock .sortable {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    white-space: nowrap;
}

#stock .sortable .sort-icon {
    font-size: 20px;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    opacity: 0.5;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .sortable:hover .sort-icon {
    opacity: 1;
}

/* Checkbox Styling */
#stock .table thead th input[type="checkbox"],
#stock .table tbody td input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--md-sys-color-primary, #1976d2);
    cursor: pointer;
}

/* Pagination */
#stock .pagination-sm {
    gap: 8px;
}

#stock .pagination-sm .page-item .page-link {
    border-radius: 50%;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    padding: 8px 12px;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    border: none;
    background: var(--md-sys-color-surface-container-low, #f9f9f9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .pagination-sm .page-item .page-link:hover {
    background: var(--md-sys-color-surface-container-high, #e0e0e0);
    color: var(--md-sys-color-primary, #1976d2);
}

#stock .pagination-sm .page-item.active .page-link {
    background: var(--md-sys-color-primary, #1976d2);
    color: var(--md-sys-color-on-primary, #ffffff);
}

#stock #stockTableInfo {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
}

/* Stock Entry Modal */
#stockEntryModal .modal-content {
    border-radius: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 16px 32px rgba(0, 0, 0, 0.15); /* Elevation 4 */
    border: none;
    background: var(--md-sys-color-surface, #ffffff);
}

#stockEntryModal .modal-header {
    border-bottom: 1px solid var(--md-sys-color-outline-variant, #e0e0e0);
    padding: 16px 24px;
}

#stockEntryModal .modal-header .modal-title {
    font-family: 'Roboto', sans-serif;
    font-size: 20px; /* Material Design 3 Headline Small */
    font-weight: 500;
    line-height: 28px;
    letter-spacing: 0.15px;
    color: var(--md-sys-color-on-surface, #1c2526);
}

#stockEntryModal .modal-header .btn-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    opacity: 0.7;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stockEntryModal .modal-header .btn-close:hover {
    opacity: 1;
}

#stockEntryModal .modal-body {
    padding: 24px;
}

#stockEntryModal .modal-body .form-label {
    font-family: 'Roboto', sans-serif;
    font-size: 12px; /* Material Design 3 Label Small */
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0.5px;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    margin-bottom: 8px;
    text-transform: uppercase;
}

#stockEntryModal .modal-body .form-control-sm {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid var(--md-sys-color-outline, #757575);
    background: var(--md-sys-color-surface-container-low, #f9f9f9);
    color: var(--md-sys-color-on-surface, #1c2526);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stockEntryModal .modal-body .form-control-sm:focus {
    border-color: var(--md-sys-color-primary, #1976d2);
    box-shadow: 0 0 0 2px var(--md-sys-color-primary-container, rgba(25, 118, 210, 0.2));
    outline: none;
}

#stockEntryModal .modal-footer {
    border-top: 1px solid var(--md-sys-color-outline-variant, #e0e0e0);
    padding: 16px 24px;
}

#stockEntryModal .modal-footer .btn-sm {
    border-radius: 28px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.1px;
    padding: 8px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stockEntryModal .modal-footer .btn-secondary {
    background: var(--md-sys-color-surface-container-high, #e0e0e0);
    border: none;
    color: var(--md-sys-color-on-surface-variant, #5f6368);
}

#stockEntryModal .modal-footer .btn-secondary:hover {
    background: var(--md-sys-color-surface-container-highest, #d0d0d0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

#stockEntryModal .modal-footer .btn-primary {
    background: var(--md-sys-color-primary, #1976d2);
    border: none;
    color: var(--md-sys-color-on-primary, #ffffff);
}

#stockEntryModal .modal-footer .btn-primary:hover {
    background: var(--md-sys-color-primary-container, #90caf9);
    color: var(--md-sys-color-on-primary-container, #0d47a1);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

/* View Toggle Buttons */
#stock .btn-group .view-btn {
    padding: 8px;
    border-radius: 50%;
    border: none;
    background: var(--md-sys-color-surface-container-low, #f9f9f9);
    color: var(--md-sys-color-on-surface-variant, #5f6368);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .btn-group .view-btn:hover {
    background: var(--md-sys-color-surface-container-high, #e0e0e0);
    color: var(--md-sys-color-primary, #1976d2);
}

#stock .btn-group .view-btn.active {
    background: var(--md-sys-color-primary, #1976d2);
    color: var(--md-sys-color-on-primary, #ffffff);
}

#stock .btn-group .view-btn .material-icons {
    font-size: 20px;
}

/* List View */
#stock .stock-list-view .list-group-item {
    border-radius: 12px;
    margin-bottom: 12px;
    border: none;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--md-sys-color-surface, #ffffff);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15); /* Elevation 1 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .stock-list-view .list-group-item:hover {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

#stock .stock-list-view .list-group-item .stock-info {
    flex-grow: 1;
}

#stock .stock-list-view .list-group-item .stock-info p {
    margin: 0 0 4px 0;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: var(--md-sys-color-on-surface, #1c2526);
}

#stock .stock-list-view .list-group-item .stock-info .badge {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    padding: 4px 8px;
}

/* Card View */
#stock .stock-card-view .card {
    border-radius: 12px;
    border: none;
    background: var(--md-sys-color-surface, #ffffff);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15); /* Elevation 1 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#stock .stock-card-view .card:hover {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15); /* Elevation 2 */
}

#stock .stock-card-view .card-body {
    padding: 16px;
}

#stock .stock-card-view .card-body p {
    margin: 0 0 4px 0;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: var(--md-sys-color-on-surface, #1c2526);
}

#stock .stock-card-view .card-body .badge {
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    padding: 4px 8px;
}

#stock .stock-card-view .card-footer {
    background: var(--md-sys-color-surface-container-low, #f9f9f9);
    border-top: 1px solid var(--md-sys-color-outline-variant, #e0e0e0);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #stock .detail-card .card-header {
        padding: 12px 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    #stock .detail-card .card-body {
        padding: 16px;
    }

    #stock .input-group-sm {
        max-width: 100%;
    }

    #stock .table thead th,
    #stock .table tbody td {
        font-size: 12px;
        padding: 8px;
    }

    #stock .pagination-sm .page-item .page-link {
        padding: 6px 10px;
        font-size: 12px;
    }

    #stock .dropdown .btn-sm,
    #stock .btn-primary.btn-sm,
    #stock .btn-danger.btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }

    #stock .dropdown .btn-sm .material-icons,
    #stock .btn-primary.btn-sm .material-icons,
    #stock .btn-danger.btn-sm .material-icons {
        font-size: 18px;
    }

    #stock .stock-card-view .row {
        --bs-columns: 2;
    }
}

@media (max-width: 576px) {
    #stock .detail-card .card-header {
        padding: 8px 12px;
    }

    #stock .detail-card .card-body {
        padding: 12px;
    }

    #stock .table thead th,
    #stock .table tbody td {
        font-size: 10px;
        padding: 6px;
    }

    #stock .pagination-sm .page-item .page-link {
        padding: 4px 8px;
        font-size: 10px;
    }

    #stock .stock-card-view .row {
        --bs-columns: 1;
    }
}

/* ===== MODERN STOCK DETAILS STYLES ===== */

/* Stock Summary Cards */
.stock-summary-cards {
    margin-bottom: 16px;
}

.stock-summary-card {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--md-white);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    height: 70px;
}

.stock-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: var(--primary-color);
}

.summary-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.summary-icon.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
}

.summary-icon.bg-success {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: var(--md-white);
}

.summary-icon.bg-warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: var(--md-white);
}

.summary-icon.bg-info {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: var(--md-white);
}

.summary-icon .material-icons {
    font-size: 20px;
}

.summary-content {
    flex: 1;
    min-width: 0;
}

.summary-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
    margin-bottom: 2px;
}

.summary-label {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

/* Modern Stock Header */
.stock-header-modern {
    background: linear-gradient(135deg, var(--md-white) 0%, var(--md-grey-50) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 16px 20px;
}

.stock-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 16px;
}

.stock-title-section {
    flex: 1;
}

.stock-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    display: flex;
    align-items: center;
}

.stock-title .material-icons {
    color: var(--primary-color);
    font-size: 18px;
}

.stock-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

.stock-actions-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.search-compact .input-group {
    width: 200px;
}

.search-compact .input-group-text {
    background: var(--md-white);
    border-color: var(--md-grey-300);
    color: var(--text-secondary);
}

.search-compact .form-control {
    border-color: var(--md-grey-300);
    font-size: 13px;
}

.search-compact .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.08);
}

.filters-compact .filter-toggle {
    position: relative;
    border-color: var(--md-grey-300);
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.filters-compact .filter-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-count {
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--status-inactive);
    color: var(--md-white);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.action-buttons-compact {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-buttons-compact .btn {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.action-buttons-compact .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.action-buttons-compact .btn-group .btn {
    padding: 6px 8px;
    min-width: 36px;
}

/* Advanced Filters Panel */
.filters-panel {
    padding: 16px 20px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
    margin: 0 -20px -16px -20px;
}

.filter-label {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.filters-panel .form-select,
.filters-panel .form-control {
    font-size: 13px;
    border-color: var(--md-grey-300);
    border-radius: 6px;
    padding: 6px 12px;
    height: auto;
}

.filters-panel .form-select:focus,
.filters-panel .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.08);
}

.filter-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.filter-actions .btn {
    font-size: 12px;
    padding: 6px 16px;
    border-radius: 6px;
    font-weight: 600;
}

/* Modern Stock Content */
.stock-content-modern {
    padding: 16px 20px;
}

/* Selection Controls */
.selection-controls-stock {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    color: var(--md-white);
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.selection-info {
    font-size: 14px;
    font-weight: 600;
}

.selection-actions {
    display: flex;
    gap: 8px;
}

.selection-actions .btn {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 6px;
    font-weight: 500;
}

/* Loading State */
.loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    background: var(--md-white);
    border-radius: 8px;
    border: 1px solid var(--md-grey-200);
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.loading-text {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Modern Table Styles */
.table-responsive-modern {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid var(--md-grey-200);
}

.table-modern {
    margin: 0;
    font-size: 13px;
    background: var(--md-white);
}

.table-header-modern {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
}

.table-header-modern th {
    border: none;
    padding: 12px 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    vertical-align: middle;
}

.table-header-modern .select-column {
    width: 40px;
    text-align: center;
}

.table-header-modern .location-col {
    min-width: 120px;
}

.table-header-modern .number-col {
    width: 100px;
    text-align: right;
}

.table-header-modern .text-col {
    width: 90px;
}

.table-header-modern .date-col {
    width: 120px;
}

.table-header-modern .actions-col {
    width: 80px;
    text-align: center;
}

.th-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    white-space: nowrap;
}

.th-content .sort-icon {
    font-size: 14px;
    color: var(--text-secondary);
    transition: color 0.2s ease;
}

.sortable:hover .sort-icon {
    color: var(--primary-color);
}

.sortable {
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    white-space: nowrap;
}

.sortable:hover {
    background-color: var(--md-grey-100);
}

.table-body-modern td {
    padding: 10px 8px;
    border-color: var(--md-grey-100);
    vertical-align: middle;
    font-size: 12px;
    color: var(--text-primary);
}

.table-body-modern tr {
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.table-body-modern tr:hover {
    background-color: var(--md-grey-50);
    transform: scale(1.005);
}

.table-body-modern .form-check {
    margin: 0;
    display: flex;
    justify-content: center;
}

.table-body-modern .form-check-input {
    margin: 0;
}

/* Modern List View */
.stock-list-modern {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stock-list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.stock-list-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.stock-list-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 16px;
    align-items: center;
}

.stock-list-field {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.stock-list-label {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stock-list-value {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

/* Modern Grid View */
.stock-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    padding: 4px;
}

.stock-grid-item {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
}

.stock-grid-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.stock-grid-item .selection-checkbox {
    position: absolute;
    top: 12px;
    right: 12px;
}

.stock-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    padding-right: 24px;
}

.stock-grid-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stock-grid-title .location-icon {
    color: var(--primary-color);
    font-size: 16px;
}

.stock-grid-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
}

.stock-grid-field {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.stock-grid-label {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stock-grid-value {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

.stock-grid-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    border-top: 1px solid var(--md-grey-100);
}

.stock-grid-actions {
    display: flex;
    gap: 4px;
}

/* List View */
.stock-list-modern {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stock-list-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.stock-list-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.stock-list-content {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 16px;
    align-items: center;
    margin-left: 12px;
}

.stock-list-field {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.stock-list-label {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stock-list-value {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

.stock-list-actions {
    display: flex;
    gap: 4px;
    align-items: center;
}

/* Bootstrap Cards View */
.stock-bootstrap-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
}

.stock-bootstrap-card {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stock-bootstrap-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.stock-bootstrap-card .card-header {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stock-bootstrap-card .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stock-bootstrap-card .card-title .warehouse-icon {
    color: var(--primary-color);
    font-size: 18px;
}

.stock-bootstrap-card .card-body {
    padding: 20px;
}

.stock-bootstrap-card .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.stock-bootstrap-card .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stock-bootstrap-card .info-label {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stock-bootstrap-card .info-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.stock-bootstrap-card .stock-metrics {
    display: flex;
    justify-content: space-around;
    padding: 12px 0;
    background: var(--md-grey-50);
    border-radius: 8px;
    margin-bottom: 16px;
}

.stock-bootstrap-card .metric-item {
    text-align: center;
}

.stock-bootstrap-card .metric-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    display: block;
}

.stock-bootstrap-card .metric-label {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stock-bootstrap-card .card-footer {
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Tiles View */
.stock-tiles-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
}

.stock-tile-item {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 8px;
    padding: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    position: relative;
    min-height: 120px;
}

.stock-tile-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.stock-tile-item .selection-checkbox {
    position: absolute;
    top: 8px;
    right: 8px;
}

.stock-tile-header {
    margin-bottom: 8px;
    padding-right: 20px;
}

.stock-tile-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 2px 0;
}

.stock-tile-subtitle {
    font-size: 11px;
    color: var(--text-secondary);
    margin: 0;
}

.stock-tile-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.stock-tile-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
}

.stock-tile-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.stock-tile-value {
    color: var(--text-primary);
    font-weight: 600;
}

.stock-tile-actions {
    position: absolute;
    bottom: 8px;
    right: 8px;
}

/* Compact View */
.stock-compact-modern {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stock-compact-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 6px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    font-size: 12px;
}

.stock-compact-item:hover {
    border-color: var(--primary-color);
    background: var(--md-grey-50);
}

.stock-compact-content {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
    gap: 12px;
    align-items: center;
}

.stock-compact-field {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.stock-compact-label {
    font-size: 9px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

.stock-compact-value {
    font-size: 11px;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.2;
}

.stock-compact-actions {
    display: flex;
    gap: 4px;
    align-items: center;
}

/* Modern Pagination */
.pagination-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0 0 0;
    border-top: 1px solid var(--md-grey-200);
    margin-top: 16px;
}

.pagination-info .info-text {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.pagination-nav .pagination {
    margin: 0;
}

.pagination-nav .page-link {
    font-size: 12px;
    padding: 6px 12px;
    border-color: var(--md-grey-300);
    color: var(--text-secondary);
    border-radius: 6px;
    margin: 0 2px;
}

.pagination-nav .page-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--md-grey-50);
}

.pagination-nav .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--md-white);
}

/* Modern Modal Styles */
.modal-modern {
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modal-header-modern {
    background: linear-gradient(135deg, var(--md-white) 0%, var(--md-grey-50) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 20px 24px;
    border-radius: 16px 16px 0 0;
}

.modal-title-section {
    flex: 1;
}

.modal-title-section .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    display: flex;
    align-items: center;
}

.modal-title-section .modal-title .material-icons {
    color: var(--primary-color);
    font-size: 20px;
}

.modal-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

.btn-close-modern {
    background: var(--md-grey-100);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.btn-close-modern:hover {
    background: var(--md-grey-200);
    transform: scale(1.1);
}

.modal-body-modern {
    padding: 24px;
}

.stock-form-modern {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-section {
    position: relative;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--md-grey-100);
}

.section-title .material-icons {
    color: var(--primary-color);
    font-size: 18px;
}

.form-group-modern {
    position: relative;
}

.form-label-modern {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 6px;
    display: block;
}

.form-control-modern {
    font-size: 13px;
    border: 1px solid var(--md-grey-300);
    border-radius: 8px;
    padding: 10px 12px;
    background: var(--md-white);
    color: var(--text-primary);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.form-control-modern:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.08);
    background: var(--md-white);
    color: var(--text-primary);
}

.modal-footer-modern {
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
    padding: 16px 24px;
    border-radius: 0 0 16px 16px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.btn-modern {
    font-size: 13px;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-modern .material-icons {
    font-size: 16px;
}

/* Responsive Design for Stock Details */
@media (max-width: 768px) {
    .stock-summary-cards .row {
        --bs-columns: 2;
    }

    .stock-summary-card {
        height: 60px;
        padding: 8px 12px;
    }

    .summary-icon {
        width: 36px;
        height: 36px;
        margin-right: 8px;
    }

    .summary-icon .material-icons {
        font-size: 16px;
    }

    .summary-value {
        font-size: 16px;
    }

    .summary-label {
        font-size: 10px;
    }

    .stock-header-content {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .stock-actions-bar {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .search-compact .input-group {
        width: 100%;
    }

    .action-buttons-compact {
        justify-content: space-between;
    }

    .table-responsive-modern {
        font-size: 11px;
    }

    .table-header-modern th {
        padding: 8px 4px;
        font-size: 10px;
    }

    .table-body-modern td {
        padding: 8px 4px;
        font-size: 11px;
    }

    .stock-cards-modern {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .stock-card-item {
        padding: 12px;
    }

    .pagination-modern {
        flex-direction: column;
        gap: 12px;
        align-items: center;
    }

    .modal-header-modern {
        padding: 16px 20px;
    }

    .modal-body-modern {
        padding: 20px;
    }

    .stock-form-modern {
        gap: 20px;
    }

    .section-title {
        font-size: 13px;
        margin-bottom: 12px;
    }

    .form-control-modern {
        font-size: 14px;
        padding: 12px;
    }
}

@media (max-width: 576px) {
    .stock-summary-cards .row {
        --bs-columns: 1;
    }

    .stock-summary-card {
        height: 50px;
        padding: 6px 10px;
    }

    .summary-icon {
        width: 32px;
        height: 32px;
        margin-right: 6px;
    }

    .summary-icon .material-icons {
        font-size: 14px;
    }

    .summary-value {
        font-size: 14px;
    }

    .summary-label {
        font-size: 9px;
    }

    .stock-header-modern {
        padding: 12px 16px;
    }

    .stock-content-modern {
        padding: 12px 16px;
    }

    .table-header-modern .compact-col {
        min-width: 60px;
    }

    .table-header-modern .number-col {
        width: 60px;
    }

    .table-header-modern .date-col {
        width: 80px;
    }

    .table-header-modern .actions-col {
        width: 50px;
    }
}

/* Pricing Tab Styles */
.pricing-summary-cards {
    margin-bottom: 20px;
}

.pricing-summary-card {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    height: 80px;
}

.pricing-summary-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.pricing-header-modern {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 16px 20px;
}

.pricing-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.pricing-title-section h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.pricing-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.pricing-actions-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.pricing-content-modern {
    padding: 16px 20px;
}

/* Manufacturer Tab Styles */
.manufacturer-summary-cards {
    margin-bottom: 20px;
}

.manufacturer-summary-card {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    height: 80px;
}

.manufacturer-summary-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.manufacturer-header-modern {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 16px 20px;
}

.manufacturer-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.manufacturer-title-section h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.manufacturer-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.manufacturer-actions-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.manufacturer-content-modern {
    padding: 16px 20px;
}

/* Assets Tab Styles */
.assets-summary-cards {
    margin-bottom: 20px;
}

.assets-summary-card {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    height: 80px;
}

.assets-summary-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.assets-header-modern {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 16px 20px;
}

.assets-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.assets-title-section h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.assets-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.assets-actions-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.assets-content-modern {
    padding: 16px 20px;
}

/* Competitor Tab Styles */
.competitor-summary-cards {
    margin-bottom: 20px;
}

.competitor-summary-card {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    height: 80px;
}

.competitor-summary-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.competitor-header-modern {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 16px 20px;
}

.competitor-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.competitor-title-section h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.competitor-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.competitor-actions-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.competitor-content-modern {
    padding: 16px 20px;
}

/* Files Tab Styles */
.files-summary-cards {
    margin-bottom: 20px;
}

.files-summary-card {
    background: var(--md-white);
    border: 1px solid var(--md-grey-200);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    height: 80px;
}

.files-summary-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.files-header-modern {
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
    padding: 16px 20px;
}

.files-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.files-title-section h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.files-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.files-actions-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.files-content-modern {
    padding: 16px 20px;
}

/* Selection Controls for All Tabs */
.selection-controls-pricing,
.selection-controls-manufacturer,
.selection-controls-assets,
.selection-controls-competitor,
.selection-controls-files {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 12px 20px;
    margin: -16px -20px 16px -20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selection-controls-pricing .selection-info,
.selection-controls-manufacturer .selection-info,
.selection-controls-assets .selection-info,
.selection-controls-competitor .selection-info,
.selection-controls-files .selection-info {
    font-weight: 500;
    font-size: 14px;
}

.selection-controls-pricing .selection-actions,
.selection-controls-manufacturer .selection-actions,
.selection-controls-assets .selection-actions,
.selection-controls-competitor .selection-actions,
.selection-controls-files .selection-actions {
    display: flex;
    gap: 8px;
}

/* View Containers for All Tabs */
.pricing-views,
.manufacturer-views,
.assets-views,
.competitor-views,
.files-views {
    min-height: 200px;
}

.pricing-view,
.manufacturer-view,
.assets-view,
.competitor-view,
.files-view {
    animation: fadeIn 0.3s ease-in-out;
}

/* Grid Views for All Tabs */
.pricing-grid-modern,
.manufacturer-grid-modern,
.assets-grid-modern,
.competitor-grid-modern,
.files-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    padding: 8px 0;
}

/* List Views for All Tabs */
.pricing-list-modern,
.manufacturer-list-modern,
.assets-list-modern,
.competitor-list-modern,
.files-list-modern {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px 0;
}

/* Bootstrap Cards Views for All Tabs */
.pricing-bootstrap-cards,
.manufacturer-bootstrap-cards,
.assets-bootstrap-cards,
.competitor-bootstrap-cards,
.files-bootstrap-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 8px 0;
}

/* Tiles Views for All Tabs */
.pricing-tiles-modern,
.manufacturer-tiles-modern,
.assets-tiles-modern,
.competitor-tiles-modern,
.files-tiles-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    padding: 8px 0;
}

/* Compact Views for All Tabs */
.pricing-compact-modern,
.manufacturer-compact-modern,
.assets-compact-modern,
.competitor-compact-modern,
.files-compact-modern {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 8px 0;
}

/* Modern Grid Card Styles for Pricing, Manufacturer, Assets, Competitor, and Files */

/* Pricing Grid Cards */
.pricing-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
    padding: 16px 0;
}

.pricing-grid-item {
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.pricing-card {
    background: var(--surface-color);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
}

.pricing-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.pricing-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
}

.pricing-type-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pricing-type-badge.standard {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
}

.pricing-type-badge.bulk {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.pricing-type-badge.premium {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
}

.pricing-type-badge.contract {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    color: white;
}

.pricing-type-badge.rush {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
}

.pricing-currency {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 12px;
    padding: 4px 8px;
    background: var(--md-grey-100);
    border-radius: 8px;
}

.pricing-card-body {
    padding: 16px;
}

.pricing-main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.list-price, .cost-price {
    text-align: center;
}

.price-label {
    display: block;
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.price-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.pricing-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.detail-item .material-icons {
    font-size: 16px;
    color: var(--primary-color);
}

.pricing-card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
}

/* Manufacturer Grid Cards */
.manufacturer-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 16px;
    padding: 16px 0;
}

.manufacturer-card {
    background: var(--surface-color);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
}

.manufacturer-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.manufacturer-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
}

.manufacturer-name {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
}

.manufacturer-status {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.manufacturer-status.status-active {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.manufacturer-status.status-inactive {
    background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
    color: white;
}

.manufacturer-card-body {
    padding: 16px;
}

.manufacturer-main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.manufacturer-part, .net-price {
    text-align: center;
}

.manufacturer-part .label, .net-price .label {
    display: block;
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.manufacturer-part .value, .net-price .value {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.manufacturer-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.manufacturer-card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
}

/* Assets Grid Cards */
.assets-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 16px;
    padding: 16px 0;
}

.assets-card {
    background: var(--surface-color);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
}

.assets-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.assets-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
}

.asset-brand {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
}

.asset-type {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
}

.assets-card-body {
    padding: 16px;
}

.asset-main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.asset-model, .asset-year {
    text-align: center;
}

.asset-model .label, .asset-year .label {
    display: block;
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.asset-model .value, .asset-year .value {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.asset-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.compatibility {
    padding: 4px 8px;
    border-radius: 8px;
    font-weight: 600;
}

.compatibility.compatibility-direct {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
}

.compatibility.compatibility-adapter {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #ef6c00;
}

.compatibility.compatibility-special {
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    color: #c2185b;
}

.asset-vin {
    margin-top: 12px;
    padding: 12px;
    background: var(--md-grey-50);
    border-radius: 8px;
}

.vin-range .label {
    display: block;
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.vin-range .value {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'Roboto Mono', monospace;
}

.assets-card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
}

/* Competitor Grid Cards */
.competitor-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 16px;
    padding: 16px 0;
}

.competitor-card {
    background: var(--surface-color);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
}

.competitor-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.competitor-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
}

.competitor-name {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
}

.competitor-availability {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.competitor-availability.availability-in-stock {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.competitor-availability.availability-limited {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
}

.competitor-availability.availability-out-stock {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
}

.competitor-card-body {
    padding: 16px;
}

.competitor-main {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
}

.net-rate, .cost-price {
    text-align: center;
}

.competitor-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.competitor-remarks {
    margin-top: 8px;
    padding: 8px;
    background: var(--md-grey-50);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.remarks-text {
    font-size: 12px;
    color: var(--text-secondary);
    font-style: italic;
}

.competitor-card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
}

/* Files Grid Cards */
.files-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
    padding: 16px 0;
}

.files-card {
    background: var(--surface-color);
    border-radius: 12px;
    border: 1px solid var(--md-grey-200);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
}

.files-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.files-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-grey-100) 100%);
    border-bottom: 1px solid var(--md-grey-200);
}

.file-type-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.file-type-icon.pdf {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.file-type-icon.excel {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.file-type-icon.image {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.file-type-icon.video {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.file-type-icon.cad {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.file-privacy {
    padding: 4px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-privacy.file-public {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.file-privacy.file-private {
    background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
    color: white;
}

.files-card-body {
    padding: 16px;
}

.file-main {
    margin-bottom: 12px;
}

.file-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-description {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 12px;
}

.file-stats {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    background: var(--md-grey-50);
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.stat-label {
    font-size: 10px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.files-card-footer {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    padding: 12px 16px;
    background: var(--md-grey-50);
    border-top: 1px solid var(--md-grey-200);
}