// Stock Details Page JavaScript

$(document).ready(function() {
    // Get stock ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const stockId = urlParams.get('id');
    const partId = urlParams.get('partId');
    
    if (stockId) {
        loadStockDetails(stockId, partId);
    } else {
        showError('Stock ID not provided');
    }
});

// Sample stock data (in real application, this would come from API)
const stockData = {
    'stock_1': {
        id: 'stock_1',
        branchName: 'Branch 11',
        binLocation: 'Rack Bin',
        warehouse: '177N-177N',
        availableStock: 0,
        reOrderLevel: 1,
        allocatedQty: 0,
        pickedQuantity: 0,
        reservedQuantity: 0,
        boQty: 0,
        minimumOrderQuantity: 1,
        weightedAverageCost: 0,
        binStock: 0,
        reOrderLevelQuantity: 1,
        pendingPurchaseOrder: 0,
        pendingPartsOrder: 0,
        goodsInTransit: 0,
        deviationStock: 0,
        totalStock: 0,
        oldBinLocation: 'Rack Bin',
        bufferBinLocation: 'Rack Bin',
        oldBufferBinLocation: '',
        serialNumber: '',
        damagedQuantity: 0,
        firstDemandDate: '',
        lastDemandDate: '',
        firstIssuedDate: '',
        lastIssuedDate: '',
        lastStockCheckDate: ''
    },
    'stock_2': {
        id: 'stock_2',
        branchName: 'Main Warehouse',
        binLocation: 'A-101',
        warehouse: 'WH-001',
        availableStock: 150,
        reOrderLevel: 50,
        allocatedQty: 25,
        pickedQuantity: 10,
        reservedQuantity: 15,
        boQty: 5,
        minimumOrderQuantity: 10,
        weightedAverageCost: 125.50,
        binStock: 150,
        reOrderLevelQuantity: 50,
        pendingPurchaseOrder: 100,
        pendingPartsOrder: 0,
        goodsInTransit: 25,
        deviationStock: 0,
        totalStock: 175,
        oldBinLocation: 'A-100',
        bufferBinLocation: 'A-102',
        oldBufferBinLocation: 'A-099',
        serialNumber: 'SN123456789',
        damagedQuantity: 2,
        firstDemandDate: '2024-01-15',
        lastDemandDate: '2024-12-10',
        firstIssuedDate: '2024-01-20',
        lastIssuedDate: '2024-12-08',
        lastStockCheckDate: '2024-12-01'
    }
};

function loadStockDetails(stockId, partId) {
    try {
        const stock = stockData[stockId];
        
        if (!stock) {
            showError('Stock not found');
            return;
        }
        
        // Populate quantity details
        $('#availableStock').text(formatNumber(stock.availableStock));
        $('#reOrderLevel').text(formatNumber(stock.reOrderLevel));
        $('#allocatedQty').text(formatNumber(stock.allocatedQty));
        $('#pickedQuantity').text(formatNumber(stock.pickedQuantity));
        $('#reservedQuantity').text(formatNumber(stock.reservedQuantity));
        $('#boQty').text(formatNumber(stock.boQty));
        $('#minimumOrderQuantity').text(formatNumber(stock.minimumOrderQuantity));
        $('#weightedAverageCost').text(formatCurrency(stock.weightedAverageCost));
        
        // Populate additional details
        $('#binStock').text(formatNumber(stock.binStock));
        $('#reOrderLevelQuantity').text(formatNumber(stock.reOrderLevelQuantity));
        $('#pendingPurchaseOrder').text(formatNumber(stock.pendingPurchaseOrder));
        $('#pendingPartsOrder').text(formatNumber(stock.pendingPartsOrder));
        $('#goodsInTransit').text(formatNumber(stock.goodsInTransit));
        $('#deviationStock').text(formatNumber(stock.deviationStock));
        $('#totalStock').text(formatNumber(stock.totalStock));
        
        // Populate location details
        $('#binLocation').text(stock.binLocation || '-');
        $('#oldBinLocation').text(stock.oldBinLocation || '-');
        $('#bufferBinLocation').text(stock.bufferBinLocation || '-');
        $('#oldBufferBinLocation').text(stock.oldBufferBinLocation || '-');
        
        // Update page title
        document.title = `Stock Details - ${stock.branchName} - Parts Management System`;
        
        // Store current stock data for actions
        window.currentStock = stock;
        window.currentPartId = partId;
        
    } catch (error) {
        console.error('Error loading stock details:', error);
        showError('Error loading stock details');
    }
}

function formatNumber(value) {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    return Number(value).toLocaleString();
}

function formatCurrency(value) {
    if (value === null || value === undefined || value === '' || value === 0) {
        return '-';
    }
    return `$${Number(value).toFixed(2)}`;
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#stock`;
    } else {
        window.location.href = 'part-details.html#stock';
    }
}

function editStock() {
    if (window.currentStock) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for stock ${window.currentStock.id} will be implemented soon!`);
        console.log('Editing stock:', window.currentStock);
    }
}

function deleteStock() {
    if (window.currentStock) {
        const confirmed = confirm(`Are you sure you want to delete this stock entry for ${window.currentStock.branchName}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the stock
            showSuccess(`Stock entry for ${window.currentStock.branchName} has been deleted!`);
            console.log('Deleting stock:', window.currentStock);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editStock();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteStock();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading stock details...</p>
        </div>
    `;
    $('.details-grid').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
