// Stock Details Page JavaScript

$(document).ready(function() {
    // Get stock ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const stockId = urlParams.get('id');
    const partId = urlParams.get('partId');

    if (stockId) {
        loadStockDetails(stockId, partId);
    } else {
        showError('Stock ID not provided');
    }
});

// Use the same JSON data structure as part-details.js
const partDetails = {
    '0000052': {
        id: '0000052',
        prefix: 'OEM',
        description: 'TWINE GUIDE',
        category: 'Accessory (Outside)',
        partType: 'Core',
        partFunctionGroup: 'Electrical',
        uom: 'Each',
        isComponent: true,
        isActive: true,
        stockDetails: [
            {
                id: 'stock_001',
                branchName: 'Branch 11',
                binLocation: 'Bin',
                warehouse: '177N-177N',
                availableStock: 0.00,
                binStock: 0.00,
                totalStock: 0.00,
                serialNumber: 0,
                damagedQuantity: 2.00,
                firstDemandDate: '15-Jan-2024',
                lastDemandDate: '28-Jan-2024',
                firstIssuedDate: '10-Jan-2024',
                lastIssuedDate: '25-Jan-2024',
                lastStockCheckDate: '01-Feb-2024'
            },
            {
                id: 'stock_002',
                branchName: 'Branch 11',
                binLocation: 'No bin',
                warehouse: '177N-NB21',
                availableStock: 0.00,
                binStock: 0.00,
                totalStock: 0.00,
                serialNumber: 0,
                damagedQuantity: 0.00,
                firstDemandDate: '20-Jan-2024',
                lastDemandDate: '30-Jan-2024',
                firstIssuedDate: '18-Jan-2024',
                lastIssuedDate: '29-Jan-2024',
                lastStockCheckDate: '02-Feb-2024'
            },
            {
                id: 'stock_003',
                branchName: 'Branch 12',
                binLocation: 'Bin A1',
                warehouse: '178N-A1B2',
                availableStock: 15.00,
                binStock: 15.00,
                totalStock: 15.00,
                serialNumber: 3,
                damagedQuantity: 1.00,
                firstDemandDate: '05-Jan-2024',
                lastDemandDate: '02-Feb-2024',
                firstIssuedDate: '08-Jan-2024',
                lastIssuedDate: '01-Feb-2024',
                lastStockCheckDate: '03-Feb-2024'
            },
            {
                id: 'stock_004',
                branchName: 'Branch 13',
                binLocation: 'Bin C3',
                warehouse: '179N-C3D4',
                availableStock: 25.00,
                binStock: 25.00,
                totalStock: 25.00,
                serialNumber: 8,
                damagedQuantity: 0.00,
                firstDemandDate: '12-Jan-2024',
                lastDemandDate: '31-Jan-2024',
                firstIssuedDate: '15-Jan-2024',
                lastIssuedDate: '30-Jan-2024',
                lastStockCheckDate: '04-Feb-2024'
            }
        ]
    }
};

function loadStockDetails(stockId, partId) {
    try {
        const part = partDetails[partId];

        if (!part || !part.stockDetails) {
            showError('Part or stock details not found');
            return;
        }

        // Find the specific stock entry
        const stock = part.stockDetails.find(s => s.id === stockId);

        if (!stock) {
            showError('Stock entry not found');
            return;
        }

        // Update header information
        $('#partNumber').text(`${part.prefix}-${part.id}`);
        $('#partDescription').text(`${part.description} - Stock Details`);
        $('#branchName').text(stock.branchName || '-');
        $('#warehouse').text(stock.warehouse || '-');
        $('#binLocation').text(stock.binLocation || '-');

        // Populate stock information
        $('#availableStock').text(formatNumber(stock.availableStock));
        $('#binStock').text(formatNumber(stock.binStock));
        $('#totalStock').text(formatNumber(stock.totalStock));
        $('#serialNumber').text(stock.serialNumber || '-');
        $('#damagedQuantity').text(formatNumber(stock.damagedQuantity));

        // Populate date information
        $('#firstDemandDate').text(stock.firstDemandDate || '-');
        $('#lastDemandDate').text(stock.lastDemandDate || '-');
        $('#firstIssuedDate').text(stock.firstIssuedDate || '-');
        $('#lastIssuedDate').text(stock.lastIssuedDate || '-');
        $('#lastStockCheckDate').text(stock.lastStockCheckDate || '-');

        // Update quick stats
        $('#statAvailableStock').text(formatNumber(stock.availableStock));
        $('#statTotalStock').text(formatNumber(stock.totalStock));
        $('#statDamagedStock').text(formatNumber(stock.damagedQuantity));
        $('#statBinLocation').text(stock.binLocation || '-');

        // Update recent activity
        $('#lastStockUpdate').text(stock.lastStockCheckDate || 'Unknown');

        // Update page title
        document.title = `Stock Details - ${stock.branchName} - ${part.description}`;

        // Store current stock data for actions
        window.currentStock = stock;
        window.currentPart = part;
        window.currentPartId = partId;

    } catch (error) {
        console.error('Error loading stock details:', error);
        showError('Error loading stock details');
    }
}

function formatNumber(value) {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    return Number(value).toLocaleString();
}

function formatCurrency(value) {
    if (value === null || value === undefined || value === '' || value === 0) {
        return '-';
    }
    return `$${Number(value).toFixed(2)}`;
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#stock`;
    } else {
        window.location.href = 'part-details.html?id=0000052#stock';
    }
}

function editStock() {
    if (window.currentStock) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for stock ${window.currentStock.id} will be implemented soon!`);
        console.log('Editing stock:', window.currentStock);
    }
}

function deleteStock() {
    if (window.currentStock) {
        const confirmed = confirm(`Are you sure you want to delete this stock entry for ${window.currentStock.branchName}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the stock
            showSuccess(`Stock entry for ${window.currentStock.branchName} has been deleted!`);
            console.log('Deleting stock:', window.currentStock);

            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }

    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editStock();
    }

    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteStock();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading stock details...</p>
        </div>
    `;
    $('.details-grid').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
