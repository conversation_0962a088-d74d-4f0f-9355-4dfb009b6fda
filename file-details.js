// File Details Page JavaScript

$(document).ready(function() {
    // Get file ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const fileId = urlParams.get('id');
    const partId = urlParams.get('partId') || '0000052';
    
    if (fileId) {
        loadFileDetails(fileId, partId);
    } else {
        showError('File ID not provided');
    }
});

// Use the same JSON data structure as part-details.js
const partDetails = {
    '0000052': {
        id: '0000052',
        prefix: 'OEM',
        description: 'TWINE GUIDE',
        category: 'Accessory (Outside)',
        partType: 'Core',
        partFunctionGroup: 'Electrical',
        uom: 'Each',
        isComponent: true,
        isActive: true,
        attachments: [
            {
                id: 'file_001',
                fileName: 'TWINE_GUIDE_Manual.pdf',
                fileDescription: 'Installation and maintenance manual for TWINE GUIDE',
                fileType: 'PDF',
                fileSize: '2.5 MB',
                uploadedBy: 'admin',
                uploadDate: '15-Nov-2024 10:30 AM',
                category: 'Manual',
                version: '1.0',
                downloadCount: 25,
                lastAccessed: '01-Dec-2024',
                tags: ['manual', 'installation', 'maintenance'],
                isPublic: true
            },
            {
                id: 'file_002',
                fileName: 'TWINE_GUIDE_Specs.xlsx',
                fileDescription: 'Technical specifications and dimensions',
                fileType: 'Excel',
                fileSize: '1.2 MB',
                uploadedBy: 'engineer',
                uploadDate: '20-Oct-2024 02:15 PM',
                category: 'Specification',
                version: '2.1',
                downloadCount: 18,
                lastAccessed: '28-Nov-2024',
                tags: ['specifications', 'technical', 'dimensions'],
                isPublic: false
            },
            {
                id: 'file_003',
                fileName: 'TWINE_GUIDE_Image.jpg',
                fileDescription: 'High-resolution product image',
                fileType: 'Image',
                fileSize: '850 KB',
                uploadedBy: 'photographer',
                uploadDate: '05-Sep-2024 09:45 AM',
                category: 'Image',
                version: '1.0',
                downloadCount: 42,
                lastAccessed: '30-Nov-2024',
                tags: ['image', 'product', 'photo'],
                isPublic: true
            },
            {
                id: 'file_004',
                fileName: 'TWINE_GUIDE_CAD.dwg',
                fileDescription: 'CAD drawing for manufacturing',
                fileType: 'CAD',
                fileSize: '3.8 MB',
                uploadedBy: 'designer',
                uploadDate: '12-Aug-2024 11:20 AM',
                category: 'Drawing',
                version: '3.0',
                downloadCount: 12,
                lastAccessed: '25-Nov-2024',
                tags: ['cad', 'drawing', 'manufacturing'],
                isPublic: false
            }
        ]
    }
};

function loadFileDetails(fileId, partId) {
    try {
        const part = partDetails[partId];
        
        if (!part || !part.attachments) {
            showError('Part or file details not found');
            return;
        }
        
        // Find the specific file entry
        const file = part.attachments.find(f => f.id === fileId);
        
        if (!file) {
            showError('File entry not found');
            return;
        }
        
        // Update header information
        $('#partNumber').text(`${part.prefix}-${part.id}`);
        $('#partDescription').text(`${part.description} - File Details`);
        $('#fileName').text(file.fileName || '-');
        $('#fileType').text(file.fileType || '-');
        $('#fileSize').text(file.fileSize || '-');
        
        // Populate file information
        $('#fileNameDetail').text(file.fileName || '-');
        $('#fileDescription').text(file.fileDescription || '-');
        $('#fileTypeDetail').text(file.fileType || '-');
        $('#fileSizeDetail').text(file.fileSize || '-');
        $('#category').text(file.category || '-');
        
        // Populate upload information
        $('#uploadedBy').text(file.uploadedBy || '-');
        $('#uploadDate').text(file.uploadDate || '-');
        $('#version').text(file.version || '-');
        $('#downloadCount').text(file.downloadCount || '0');
        $('#lastAccessed').text(file.lastAccessed || '-');
        
        // Populate tags
        if (file.tags && file.tags.length > 0) {
            const tagsHtml = file.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('');
            $('#fileTags').html(tagsHtml);
        } else {
            $('#fileTags').text('-');
        }
        
        // Update quick stats
        $('#statFileType').text(file.fileType || '-');
        $('#statFileSize').text(file.fileSize || '-');
        $('#statDownloads').text(file.downloadCount || '0');
        $('#statPublic').text(file.isPublic ? 'Public' : 'Private');
        
        // Update recent activity
        $('#lastDownload').text(file.lastAccessed || 'Unknown');
        
        // Update page title
        document.title = `File Details - ${file.fileName} - ${part.description}`;
        
        // Store current file data for actions
        window.currentFile = file;
        window.currentPart = part;
        window.currentPartId = partId;
        
    } catch (error) {
        console.error('Error loading file details:', error);
        showError('Error loading file details');
    }
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#attachments`;
    } else {
        window.location.href = 'part-details.html?id=0000052#attachments';
    }
}

function downloadFile() {
    if (window.currentFile) {
        // In a real application, this would trigger file download
        showSuccess(`Download started for ${window.currentFile.fileName}!`);
        console.log('Downloading file:', window.currentFile);
    }
}

function editFile() {
    if (window.currentFile) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for file ${window.currentFile.fileName} will be implemented soon!`);
        console.log('Editing file:', window.currentFile);
    }
}

function deleteFile() {
    if (window.currentFile) {
        const confirmed = confirm(`Are you sure you want to delete the file ${window.currentFile.fileName}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the file
            showSuccess(`File ${window.currentFile.fileName} has been deleted!`);
            console.log('Deleting file:', window.currentFile);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+D to download
    if (e.ctrlKey && e.keyCode === 68) {
        e.preventDefault();
        downloadFile();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editFile();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteFile();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading file details...</p>
        </div>
    `;
    $('.main-content').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
