<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Details - Parts Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="part-details.css">
</head>
<body>
    <div class="container-fluid">
        <!-- Header Section - Same as part-details.html -->
        <div class="part-header">
            <div class="part-header-content">
                <div class="part-header-left">
                    <div class="part-number-section">
                        <span class="part-number" id="partNumber">Loading...</span>
                        <span class="part-status-badge status-active" id="partStatus">Active</span>
                    </div>
                    <h1 class="part-description" id="partDescription">File Details</h1>
                    <div class="part-meta">
                        <div class="meta-item">
                            <span class="meta-label">File Name:</span>
                            <span class="meta-value" id="fileName">Loading...</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">File Type:</span>
                            <span class="meta-value" id="fileType">Loading...</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">File Size:</span>
                            <span class="meta-value" id="fileSize">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="part-header-right">
                    <div class="header-actions">
                        <button class="btn btn-outline-secondary" onclick="goBack()">
                            <i class="material-icons me-2">arrow_back</i>
                            Back to Part Details
                        </button>
                        <button class="btn btn-success" onclick="downloadFile()">
                            <i class="material-icons me-2">download</i>
                            Download File
                        </button>
                        <button class="btn btn-primary" onclick="editFile()">
                            <i class="material-icons me-2">edit</i>
                            Edit File
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteFile()">
                            <i class="material-icons me-2">delete</i>
                            Delete File
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <div class="row">
                <!-- File Information Cards -->
                <div class="col-lg-8">
                    <div class="detail-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="material-icons">description</i>
                                File Information
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h5 class="info-group-title">Basic Information</h5>
                                        <div class="info-item">
                                            <label class="info-label">File Name</label>
                                            <span class="info-value" id="fileNameDetail">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">File Description</label>
                                            <span class="info-value" id="fileDescription">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">File Type</label>
                                            <span class="info-value" id="fileTypeDetail">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">File Size</label>
                                            <span class="info-value" id="fileSizeDetail">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Category</label>
                                            <span class="info-value" id="category">-</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h5 class="info-group-title">Upload Information</h5>
                                        <div class="info-item">
                                            <label class="info-label">Uploaded By</label>
                                            <span class="info-value" id="uploadedBy">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Upload Date</label>
                                            <span class="info-value" id="uploadDate">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Version</label>
                                            <span class="info-value" id="version">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Download Count</label>
                                            <span class="info-value" id="downloadCount">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Last Accessed</label>
                                            <span class="info-value" id="lastAccessed">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="info-group">
                                        <h5 class="info-group-title">Tags</h5>
                                        <div class="info-item">
                                            <div id="fileTags">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar with Quick Stats -->
                <div class="col-lg-4">
                    <div class="detail-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="material-icons">analytics</i>
                                Quick Stats
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <div class="stat-icon bg-primary">
                                    <i class="material-icons">description</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statFileType">-</div>
                                    <div class="stat-label">File Type</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon bg-success">
                                    <i class="material-icons">storage</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statFileSize">-</div>
                                    <div class="stat-label">File Size</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon bg-warning">
                                    <i class="material-icons">download</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statDownloads">0</div>
                                    <div class="stat-label">Downloads</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon bg-info">
                                    <i class="material-icons">visibility</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statPublic">-</div>
                                    <div class="stat-label">Visibility</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activity -->
                    <div class="detail-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="material-icons">history</i>
                                Recent Activity
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="activity-item">
                                <div class="activity-icon bg-primary">
                                    <i class="material-icons">visibility</i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">File viewed</div>
                                    <div class="activity-time">Just now</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon bg-success">
                                    <i class="material-icons">download</i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Last downloaded</div>
                                    <div class="activity-time" id="lastDownload">Loading...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="file-details.js"></script>
</body>
</html>
