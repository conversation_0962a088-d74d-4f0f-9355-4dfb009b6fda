<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Details - Parts Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="part-details.css">
    
    <style>
        .details-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .details-header {
            background: linear-gradient(135deg, var(--md-surface) 0%, var(--md-surface-container) 100%);
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: var(--md-elevation-2);
            border: 1px solid var(--md-outline-variant);
        }
        
        .details-title {
            font-size: var(--md-typescale-headline-medium);
            font-weight: 600;
            color: var(--md-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .details-subtitle {
            font-size: var(--md-typescale-body-large);
            color: var(--md-on-surface-variant);
            margin-bottom: 24px;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        
        .details-card {
            background: var(--md-surface);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--md-elevation-1);
            border: 1px solid var(--md-outline-variant);
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-standard);
        }
        
        .details-card:hover {
            box-shadow: var(--md-elevation-2);
            transform: translateY(-2px);
        }
        
        .details-card-title {
            font-size: var(--md-typescale-title-medium);
            font-weight: 600;
            color: var(--md-on-surface);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .details-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--md-outline-variant);
        }
        
        .details-field:last-child {
            border-bottom: none;
        }
        
        .details-label {
            font-size: var(--md-typescale-body-medium);
            font-weight: 500;
            color: var(--md-on-surface-variant);
        }
        
        .details-value {
            font-size: var(--md-typescale-body-medium);
            font-weight: 600;
            color: var(--md-on-surface);
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 32px;
        }
        
        .btn-modern {
            border-radius: 20px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-emphasized);
            box-shadow: var(--md-elevation-1);
        }
        
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--md-elevation-2);
        }
        
        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin-bottom: 24px;
        }
        
        .breadcrumb-modern .breadcrumb-item a {
            color: var(--md-on-surface-variant);
            text-decoration: none;
            transition: color var(--md-motion-duration-short2) var(--md-motion-easing-standard);
        }
        
        .breadcrumb-modern .breadcrumb-item a:hover {
            color: var(--md-primary);
        }
        
        .breadcrumb-modern .breadcrumb-item.active {
            color: var(--md-on-surface);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="details-container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-modern">
                <li class="breadcrumb-item"><a href="part-details.html">Part Details</a></li>
                <li class="breadcrumb-item"><a href="part-details.html#assets">Assets</a></li>
                <li class="breadcrumb-item active" aria-current="page">Asset Details</li>
            </ol>
        </nav>
        
        <!-- Header -->
        <div class="details-header">
            <h1 class="details-title">
                <i class="material-icons">directions_car</i>
                Asset Details
            </h1>
            <p class="details-subtitle">Comprehensive asset information and specifications</p>
        </div>
        
        <!-- Details Grid -->
        <div class="details-grid">
            <!-- Basic Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">info</i>
                    Basic Information
                </h3>
                <div class="details-field">
                    <span class="details-label">Brand</span>
                    <span class="details-value" id="brand">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Model</span>
                    <span class="details-value" id="model">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Year</span>
                    <span class="details-value" id="year">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Engine</span>
                    <span class="details-value" id="engine">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Fuel Type</span>
                    <span class="details-value" id="fuelType">-</span>
                </div>
            </div>
            
            <!-- Technical Specifications -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">build</i>
                    Technical Specifications
                </h3>
                <div class="details-field">
                    <span class="details-label">Transmission</span>
                    <span class="details-value" id="transmission">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Drive Type</span>
                    <span class="details-value" id="driveType">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Body Type</span>
                    <span class="details-value" id="bodyType">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Doors</span>
                    <span class="details-value" id="doors">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Seats</span>
                    <span class="details-value" id="seats">-</span>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">description</i>
                    Additional Details
                </h3>
                <div class="details-field">
                    <span class="details-label">VIN</span>
                    <span class="details-value" id="vin">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Registration</span>
                    <span class="details-value" id="registration">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Color</span>
                    <span class="details-value" id="color">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Mileage</span>
                    <span class="details-value" id="mileage">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Status</span>
                    <span class="details-value" id="status">-</span>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-outline-secondary btn-modern" onclick="goBack()">
                <i class="material-icons me-2">arrow_back</i>
                Back to Part Details
            </button>
            <button class="btn btn-primary btn-modern" onclick="editAsset()">
                <i class="material-icons me-2">edit</i>
                Edit Asset
            </button>
            <button class="btn btn-outline-danger btn-modern" onclick="deleteAsset()">
                <i class="material-icons me-2">delete</i>
                Delete Asset
            </button>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="asset-details.js"></script>
</body>
</html>
