<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Details - Parts Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="part-details.css">
    
    <style>
        .details-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .details-header {
            background: linear-gradient(135deg, var(--md-surface) 0%, var(--md-surface-container) 100%);
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: var(--md-elevation-2);
            border: 1px solid var(--md-outline-variant);
        }
        
        .details-title {
            font-size: var(--md-typescale-headline-medium);
            font-weight: 600;
            color: var(--md-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .details-subtitle {
            font-size: var(--md-typescale-body-large);
            color: var(--md-on-surface-variant);
            margin-bottom: 24px;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        
        .details-card {
            background: var(--md-surface);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--md-elevation-1);
            border: 1px solid var(--md-outline-variant);
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-standard);
        }
        
        .details-card:hover {
            box-shadow: var(--md-elevation-2);
            transform: translateY(-2px);
        }
        
        .details-card-title {
            font-size: var(--md-typescale-title-medium);
            font-weight: 600;
            color: var(--md-on-surface);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .details-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--md-outline-variant);
        }
        
        .details-field:last-child {
            border-bottom: none;
        }
        
        .details-label {
            font-size: var(--md-typescale-body-medium);
            font-weight: 500;
            color: var(--md-on-surface-variant);
        }
        
        .details-value {
            font-size: var(--md-typescale-body-medium);
            font-weight: 600;
            color: var(--md-on-surface);
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 32px;
        }
        
        .btn-modern {
            border-radius: 20px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-emphasized);
            box-shadow: var(--md-elevation-1);
        }
        
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--md-elevation-2);
        }
        
        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin-bottom: 24px;
        }
        
        .breadcrumb-modern .breadcrumb-item a {
            color: var(--md-on-surface-variant);
            text-decoration: none;
            transition: color var(--md-motion-duration-short2) var(--md-motion-easing-standard);
        }
        
        .breadcrumb-modern .breadcrumb-item a:hover {
            color: var(--md-primary);
        }
        
        .breadcrumb-modern .breadcrumb-item.active {
            color: var(--md-on-surface);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Section - Same as part-details.html -->
        <div class="part-header">
            <div class="part-header-content">
                <div class="part-header-left">
                    <div class="part-number-section">
                        <span class="part-number" id="partNumber">Loading...</span>
                        <span class="part-status-badge status-active" id="partStatus">Active</span>
                    </div>
                    <h1 class="part-description" id="partDescription">Asset Details</h1>
                    <div class="part-meta">
                        <div class="meta-item">
                            <span class="meta-label">Brand:</span>
                            <span class="meta-value" id="assetBrand">Loading...</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Model:</span>
                            <span class="meta-value" id="assetModel">Loading...</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Year Range:</span>
                            <span class="meta-value" id="yearRange">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="part-header-right">
                    <div class="header-actions">
                        <button class="btn btn-outline-secondary" onclick="goBack()">
                            <i class="material-icons me-2">arrow_back</i>
                            Back to Part Details
                        </button>
                        <button class="btn btn-primary" onclick="editAsset()">
                            <i class="material-icons me-2">edit</i>
                            Edit Asset
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteAsset()">
                            <i class="material-icons me-2">delete</i>
                            Delete Asset
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <div class="row">
                <!-- Asset Information Cards -->
                <div class="col-lg-8">
                    <div class="detail-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="material-icons">directions_car</i>
                                Asset Information
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h5 class="info-group-title">Basic Information</h5>
                                        <div class="info-item">
                                            <label class="info-label">Brand</label>
                                            <span class="info-value" id="brand">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Asset Type</label>
                                            <span class="info-value" id="assetType">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Model</label>
                                            <span class="info-value" id="model">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Year From</label>
                                            <span class="info-value" id="yearFrom">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Year To</label>
                                            <span class="info-value" id="yearTo">-</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <h5 class="info-group-title">Technical Details</h5>
                                        <div class="info-item">
                                            <label class="info-label">Engine Type</label>
                                            <span class="info-value" id="engineType">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">From VIN</label>
                                            <span class="info-value" id="fromVin">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">To VIN</label>
                                            <span class="info-value" id="toVin">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Compatibility</label>
                                            <span class="info-value" id="compatibility">-</span>
                                        </div>
                                        <div class="info-item">
                                            <label class="info-label">Notes</label>
                                            <span class="info-value" id="notes">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar with Quick Stats -->
                <div class="col-lg-4">
                    <div class="detail-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="material-icons">analytics</i>
                                Quick Stats
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <div class="stat-icon bg-primary">
                                    <i class="material-icons">directions_car</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statBrand">-</div>
                                    <div class="stat-label">Brand</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon bg-success">
                                    <i class="material-icons">model_training</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statModel">-</div>
                                    <div class="stat-label">Model</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon bg-warning">
                                    <i class="material-icons">date_range</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statYearRange">-</div>
                                    <div class="stat-label">Year Range</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon bg-info">
                                    <i class="material-icons">check_circle</i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-value" id="statCompatibility">-</div>
                                    <div class="stat-label">Compatibility</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="detail-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="material-icons">history</i>
                                Recent Activity
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="activity-item">
                                <div class="activity-icon bg-primary">
                                    <i class="material-icons">visibility</i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Asset viewed</div>
                                    <div class="activity-time">Just now</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon bg-success">
                                    <i class="material-icons">update</i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Asset updated</div>
                                    <div class="activity-time" id="lastAssetUpdate">Loading...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="asset-details.js"></script>
</body>
</html>
