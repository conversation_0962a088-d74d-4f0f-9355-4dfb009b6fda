// Pricing Details Page JavaScript

$(document).ready(function() {
    // Get pricing ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const pricingId = urlParams.get('id');
    const partId = urlParams.get('partId');
    
    if (pricingId) {
        loadPricingDetails(pricingId, partId);
    } else {
        showError('Pricing ID not provided');
    }
});

// Sample pricing data (in real application, this would come from API)
const pricingData = {
    'pricing_1': {
        id: 'pricing_1',
        effectiveFrom: '2024-01-01',
        effectiveTo: '2024-12-31',
        currency: 'USD',
        status: 'Active',
        listPrice: 150.00,
        netPrice: 135.00,
        discountPercent: 10.0,
        costPrice: 100.00,
        marginPercent: 35.0,
        priceType: 'Standard',
        minimumQuantity: 1,
        maximumQuantity: 1000,
        createdDate: '2024-01-01',
        lastModified: '2024-12-01'
    },
    'pricing_2': {
        id: 'pricing_2',
        effectiveFrom: '2024-06-01',
        effectiveTo: '2025-05-31',
        currency: 'EUR',
        status: 'Active',
        listPrice: 200.00,
        netPrice: 180.00,
        discountPercent: 10.0,
        costPrice: 140.00,
        marginPercent: 28.6,
        priceType: 'Volume',
        minimumQuantity: 10,
        maximumQuantity: 500,
        createdDate: '2024-06-01',
        lastModified: '2024-11-15'
    }
};

function loadPricingDetails(pricingId, partId) {
    try {
        const pricing = pricingData[pricingId];
        
        if (!pricing) {
            showError('Pricing not found');
            return;
        }
        
        // Populate basic information
        $('#effectiveFrom').text(pricing.effectiveFrom || '-');
        $('#effectiveTo').text(pricing.effectiveTo || '-');
        $('#currency').text(pricing.currency || '-');
        $('#status').html(pricing.status ? `<span class="badge bg-success">${pricing.status}</span>` : '-');
        
        // Populate price information
        $('#listPrice').text(formatCurrency(pricing.listPrice));
        $('#netPrice').text(formatCurrency(pricing.netPrice));
        $('#discountPercent').text(pricing.discountPercent ? `${pricing.discountPercent}%` : '-');
        $('#costPrice').text(formatCurrency(pricing.costPrice));
        $('#marginPercent').text(pricing.marginPercent ? `${pricing.marginPercent.toFixed(1)}%` : '-');
        
        // Populate additional details
        $('#priceType').text(pricing.priceType || '-');
        $('#minimumQuantity').text(formatNumber(pricing.minimumQuantity));
        $('#maximumQuantity').text(formatNumber(pricing.maximumQuantity));
        $('#createdDate').text(pricing.createdDate || '-');
        $('#lastModified').text(pricing.lastModified || '-');
        
        // Update page title
        document.title = `Pricing Details - ${pricing.effectiveFrom} - Parts Management System`;
        
        // Store current pricing data for actions
        window.currentPricing = pricing;
        window.currentPartId = partId;
        
    } catch (error) {
        console.error('Error loading pricing details:', error);
        showError('Error loading pricing details');
    }
}

function formatNumber(value) {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    return Number(value).toLocaleString();
}

function formatCurrency(value) {
    if (value === null || value === undefined || value === '' || value === 0) {
        return '-';
    }
    return `$${Number(value).toFixed(2)}`;
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#pricing`;
    } else {
        window.location.href = 'part-details.html#pricing';
    }
}

function editPricing() {
    if (window.currentPricing) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for pricing ${window.currentPricing.id} will be implemented soon!`);
        console.log('Editing pricing:', window.currentPricing);
    }
}

function deletePricing() {
    if (window.currentPricing) {
        const confirmed = confirm(`Are you sure you want to delete this pricing entry effective from ${window.currentPricing.effectiveFrom}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the pricing
            showSuccess(`Pricing entry effective from ${window.currentPricing.effectiveFrom} has been deleted!`);
            console.log('Deleting pricing:', window.currentPricing);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editPricing();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deletePricing();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading pricing details...</p>
        </div>
    `;
    $('.details-grid').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
