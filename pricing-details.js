// Pricing Details Page JavaScript

$(document).ready(function() {
    // Get pricing ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const pricingId = urlParams.get('id');
    const partId = urlParams.get('partId') || '0000052';

    if (pricingId) {
        loadPricingDetails(pricingId, partId);
    } else {
        showError('Pricing ID not provided');
    }
});

// Use the same JSON data structure as part-details.js
const partDetails = {
    '0000052': {
        id: '0000052',
        prefix: 'OEM',
        description: 'TWINE GUIDE',
        category: 'Accessory (Outside)',
        partType: 'Core',
        partFunctionGroup: 'Electrical',
        uom: 'Each',
        isComponent: true,
        isActive: true,
        pricingDetails: [
            {
                id: 'pricing_001',
                priceType: 'Standard',
                listPrice: 100.00,
                costPrice: 95.00,
                effectiveFrom: '01-Sep-2023',
                customerType: 'Retail',
                customerWarranty: '12 months',
                buyingCurrency: 'USD',
                validUntil: '31-Aug-2024',
                discountPercent: 5.0,
                netPrice: 95.00
            },
            {
                id: 'pricing_002',
                priceType: 'Wholesale',
                listPrice: 85.00,
                costPrice: 80.00,
                effectiveFrom: '01-Oct-2023',
                customerType: 'Wholesale',
                customerWarranty: '6 months',
                buyingCurrency: 'USD',
                validUntil: '30-Sep-2024',
                discountPercent: 10.0,
                netPrice: 76.50
            },
            {
                id: 'pricing_003',
                priceType: 'Premium',
                listPrice: 120.00,
                costPrice: 110.00,
                effectiveFrom: '15-Nov-2023',
                customerType: 'Premium',
                customerWarranty: '24 months',
                buyingCurrency: 'USD',
                validUntil: '14-Nov-2024',
                discountPercent: 0.0,
                netPrice: 120.00
            },
            {
                id: 'pricing_004',
                priceType: 'Bulk',
                listPrice: 75.00,
                costPrice: 70.00,
                effectiveFrom: '01-Dec-2023',
                customerType: 'Bulk',
                customerWarranty: '3 months',
                buyingCurrency: 'USD',
                validUntil: '30-Nov-2024',
                discountPercent: 15.0,
                netPrice: 63.75
            }
        ]
    }
};

function loadPricingDetails(pricingId, partId) {
    try {
        const part = partDetails[partId];

        if (!part || !part.pricingDetails) {
            showError('Part or pricing details not found');
            return;
        }

        // Find the specific pricing entry
        const pricing = part.pricingDetails.find(p => p.id === pricingId);

        if (!pricing) {
            showError('Pricing entry not found');
            return;
        }

        // Update header information
        $('#partNumber').text(`${part.prefix}-${part.id}`);
        $('#partDescription').text(`${part.description} - Pricing Details`);
        $('#priceType').text(pricing.priceType || '-');
        $('#currency').text(pricing.buyingCurrency || '-');
        $('#effectiveFrom').text(pricing.effectiveFrom || '-');

        // Populate pricing information
        $('#priceTypeDetail').text(pricing.priceType || '-');
        $('#listPrice').text(formatCurrency(pricing.listPrice));
        $('#costPrice').text(formatCurrency(pricing.costPrice));
        $('#netPrice').text(formatCurrency(pricing.netPrice));
        $('#buyingCurrency').text(pricing.buyingCurrency || '-');

        // Populate additional details
        $('#effectiveFromDetail').text(pricing.effectiveFrom || '-');
        $('#validUntil').text(pricing.validUntil || '-');
        $('#customerType').text(pricing.customerType || '-');
        $('#customerWarranty').text(pricing.customerWarranty || '-');
        $('#discountPercent').text(pricing.discountPercent ? `${pricing.discountPercent}%` : '-');

        // Update quick stats
        $('#statListPrice').text(formatCurrency(pricing.listPrice));
        $('#statCostPrice').text(formatCurrency(pricing.costPrice));
        $('#statDiscount').text(pricing.discountPercent ? `${pricing.discountPercent}%` : '0%');
        $('#statCurrency').text(pricing.buyingCurrency || '-');

        // Update recent activity
        $('#lastPriceUpdate').text(pricing.effectiveFrom || 'Unknown');

        // Update page title
        document.title = `Pricing Details - ${pricing.priceType} - ${part.description}`;

        // Store current pricing data for actions
        window.currentPricing = pricing;
        window.currentPart = part;
        window.currentPartId = partId;

    } catch (error) {
        console.error('Error loading pricing details:', error);
        showError('Error loading pricing details');
    }
}

function formatNumber(value) {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    return Number(value).toLocaleString();
}

function formatCurrency(value) {
    if (value === null || value === undefined || value === '' || value === 0) {
        return '-';
    }
    return `$${Number(value).toFixed(2)}`;
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#pricing`;
    } else {
        window.location.href = 'part-details.html?id=0000052#pricing';
    }
}

function editPricing() {
    if (window.currentPricing) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for pricing ${window.currentPricing.id} will be implemented soon!`);
        console.log('Editing pricing:', window.currentPricing);
    }
}

function deletePricing() {
    if (window.currentPricing) {
        const confirmed = confirm(`Are you sure you want to delete this pricing entry effective from ${window.currentPricing.effectiveFrom}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the pricing
            showSuccess(`Pricing entry effective from ${window.currentPricing.effectiveFrom} has been deleted!`);
            console.log('Deleting pricing:', window.currentPricing);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editPricing();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deletePricing();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading pricing details...</p>
        </div>
    `;
    $('.details-grid').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
