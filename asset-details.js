// Asset Details Page JavaScript

$(document).ready(function() {
    // Get asset ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const assetId = urlParams.get('id');
    const partId = urlParams.get('partId');
    
    if (assetId) {
        loadAssetDetails(assetId, partId);
    } else {
        showError('Asset ID not provided');
    }
});

// Sample asset data (in real application, this would come from API)
const assetData = {
    'asset_1': {
        id: 'asset_1',
        brand: 'Toyota',
        model: 'Camry',
        year: 2022,
        engine: '2.5L 4-Cylinder',
        fuelType: 'Gasoline',
        transmission: 'Automatic',
        driveType: 'FWD',
        bodyType: 'Sedan',
        doors: 4,
        seats: 5,
        vin: '4T1G11AK5NU123456',
        registration: 'ABC-1234',
        color: 'Silver',
        mileage: '25,000 miles',
        status: 'Active'
    },
    'asset_2': {
        id: 'asset_2',
        brand: 'Honda',
        model: 'Civic',
        year: 2021,
        engine: '1.5L Turbo',
        fuelType: 'Gasoline',
        transmission: 'CVT',
        driveType: 'FWD',
        bodyType: 'Hatchback',
        doors: 4,
        seats: 5,
        vin: '2HGFC2F59MH123456',
        registration: 'XYZ-5678',
        color: 'Blue',
        mileage: '18,500 miles',
        status: 'Active'
    }
};

function loadAssetDetails(assetId, partId) {
    try {
        const asset = assetData[assetId];
        
        if (!asset) {
            showError('Asset not found');
            return;
        }
        
        // Populate basic information
        $('#brand').text(asset.brand || '-');
        $('#model').text(asset.model || '-');
        $('#year').text(asset.year || '-');
        $('#engine').text(asset.engine || '-');
        $('#fuelType').text(asset.fuelType || '-');
        
        // Populate technical specifications
        $('#transmission').text(asset.transmission || '-');
        $('#driveType').text(asset.driveType || '-');
        $('#bodyType').text(asset.bodyType || '-');
        $('#doors').text(asset.doors || '-');
        $('#seats').text(asset.seats || '-');
        
        // Populate additional information
        $('#vin').text(asset.vin || '-');
        $('#registration').text(asset.registration || '-');
        $('#color').text(asset.color || '-');
        $('#mileage').text(asset.mileage || '-');
        $('#status').html(asset.status === 'Active' ? 
            '<span class="badge bg-success">Active</span>' : 
            '<span class="badge bg-secondary">Inactive</span>');
        
        // Update page title
        document.title = `Asset Details - ${asset.brand} ${asset.model} - Parts Management System`;
        
        // Store current asset data for actions
        window.currentAsset = asset;
        window.currentPartId = partId;
        
    } catch (error) {
        console.error('Error loading asset details:', error);
        showError('Error loading asset details');
    }
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#assets`;
    } else {
        window.location.href = 'part-details.html#assets';
    }
}

function editAsset() {
    if (window.currentAsset) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for asset ${window.currentAsset.brand} ${window.currentAsset.model} will be implemented soon!`);
        console.log('Editing asset:', window.currentAsset);
    }
}

function deleteAsset() {
    if (window.currentAsset) {
        const confirmed = confirm(`Are you sure you want to delete this asset entry for ${window.currentAsset.brand} ${window.currentAsset.model}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the asset
            showSuccess(`Asset entry for ${window.currentAsset.brand} ${window.currentAsset.model} has been deleted!`);
            console.log('Deleting asset:', window.currentAsset);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editAsset();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteAsset();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading asset details...</p>
        </div>
    `;
    $('.details-grid').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
