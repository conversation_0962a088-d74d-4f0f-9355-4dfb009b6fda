// Asset Details Page JavaScript

$(document).ready(function() {
    // Get asset ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const assetId = urlParams.get('id');
    const partId = urlParams.get('partId') || '0000052';

    if (assetId) {
        loadAssetDetails(assetId, partId);
    } else {
        showError('Asset ID not provided');
    }
});

// Use the same JSON data structure as part-details.js
const partDetails = {
    '0000052': {
        id: '0000052',
        prefix: 'OEM',
        description: 'TWINE GUIDE',
        category: 'Accessory (Outside)',
        partType: 'Core',
        partFunctionGroup: 'Electrical',
        uom: 'Each',
        isComponent: true,
        isActive: true,
        assetDetails: [
            {
                id: 'asset_001',
                brand: 'Toyota',
                assetType: 'Vehicle',
                model: 'Camry',
                yearFrom: '2018',
                yearTo: '2024',
                engineType: '2.5L 4-Cylinder',
                fromVin: '4T1G11AK5NU000000',
                toVin: '4T1G11AK5NU999999',
                compatibility: 'Full',
                notes: 'Compatible with all trim levels'
            },
            {
                id: 'asset_002',
                brand: 'Honda',
                assetType: 'Vehicle',
                model: 'Civic',
                yearFrom: '2019',
                yearTo: '2023',
                engineType: '1.5L Turbo',
                fromVin: '2HGFC2F59MH000000',
                toVin: '2HGFC2F59MH999999',
                compatibility: 'Partial',
                notes: 'Compatible with Sport and Touring models only'
            },
            {
                id: 'asset_003',
                brand: 'Ford',
                assetType: 'Vehicle',
                model: 'F-150',
                yearFrom: '2020',
                yearTo: '2024',
                engineType: '3.5L V6',
                fromVin: '1FTFW1E50LFA00000',
                toVin: '1FTFW1E50LFA99999',
                compatibility: 'Full',
                notes: 'Compatible with all cab configurations'
            },
            {
                id: 'asset_004',
                brand: 'Chevrolet',
                assetType: 'Vehicle',
                model: 'Silverado',
                yearFrom: '2019',
                yearTo: '2023',
                engineType: '5.3L V8',
                fromVin: '1GCUYDED5KZ000000',
                toVin: '1GCUYDED5KZ999999',
                compatibility: 'Full',
                notes: 'Compatible with 1500 series'
            }
        ]
    }
};

function loadAssetDetails(assetId, partId) {
    try {
        const part = partDetails[partId];

        if (!part || !part.assetDetails) {
            showError('Part or asset details not found');
            return;
        }

        // Find the specific asset entry
        const asset = part.assetDetails.find(a => a.id === assetId);

        if (!asset) {
            showError('Asset entry not found');
            return;
        }

        // Update header information
        $('#partNumber').text(`${part.prefix}-${part.id}`);
        $('#partDescription').text(`${part.description} - Asset Details`);
        $('#assetBrand').text(asset.brand || '-');
        $('#assetModel').text(asset.model || '-');
        $('#yearRange').text(`${asset.yearFrom || '-'} - ${asset.yearTo || '-'}`);

        // Populate asset information
        $('#brand').text(asset.brand || '-');
        $('#assetType').text(asset.assetType || '-');
        $('#model').text(asset.model || '-');
        $('#yearFrom').text(asset.yearFrom || '-');
        $('#yearTo').text(asset.yearTo || '-');

        // Populate technical details
        $('#engineType').text(asset.engineType || '-');
        $('#fromVin').text(asset.fromVin || '-');
        $('#toVin').text(asset.toVin || '-');
        $('#compatibility').html(getCompatibilityBadge(asset.compatibility));
        $('#notes').text(asset.notes || '-');

        // Update quick stats
        $('#statBrand').text(asset.brand || '-');
        $('#statModel').text(asset.model || '-');
        $('#statYearRange').text(`${asset.yearFrom || '-'} - ${asset.yearTo || '-'}`);
        $('#statCompatibility').text(asset.compatibility || '-');

        // Update recent activity
        $('#lastAssetUpdate').text('Recently updated');

        // Update page title
        document.title = `Asset Details - ${asset.brand} ${asset.model} - ${part.description}`;

        // Store current asset data for actions
        window.currentAsset = asset;
        window.currentPart = part;
        window.currentPartId = partId;

    } catch (error) {
        console.error('Error loading asset details:', error);
        showError('Error loading asset details');
    }
}

function getCompatibilityBadge(compatibility) {
    switch (compatibility) {
        case 'Full':
            return '<span class="badge bg-success">Full Compatibility</span>';
        case 'Partial':
            return '<span class="badge bg-warning">Partial Compatibility</span>';
        case 'Limited':
            return '<span class="badge bg-danger">Limited Compatibility</span>';
        default:
            return '<span class="badge bg-secondary">Unknown</span>';
    }
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#assets`;
    } else {
        window.location.href = 'part-details.html?id=0000052#assets';
    }
}

function editAsset() {
    if (window.currentAsset) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for asset ${window.currentAsset.brand} ${window.currentAsset.model} will be implemented soon!`);
        console.log('Editing asset:', window.currentAsset);
    }
}

function deleteAsset() {
    if (window.currentAsset) {
        const confirmed = confirm(`Are you sure you want to delete this asset entry for ${window.currentAsset.brand} ${window.currentAsset.model}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the asset
            showSuccess(`Asset entry for ${window.currentAsset.brand} ${window.currentAsset.model} has been deleted!`);
            console.log('Deleting asset:', window.currentAsset);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editAsset();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteAsset();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading asset details...</p>
        </div>
    `;
    $('.main-content').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
