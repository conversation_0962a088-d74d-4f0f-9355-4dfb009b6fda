/* Material Design Black & White Theme Variables */
:root {
    /* Material Design Black & White Color Palette */
    --md-black: #000000;
    --md-white: #ffffff;
    --md-grey-50: #fafafa;
    --md-grey-100: #f5f5f5;
    --md-grey-200: #eeeeee;
    --md-grey-300: #e0e0e0;
    --md-grey-400: #bdbdbd;
    --md-grey-500: #9e9e9e;
    --md-grey-600: #757575;
    --md-grey-700: #616161;
    --md-grey-800: #424242;
    --md-grey-900: #212121;

    /* Primary Colors */
    --primary-color: var(--md-black);
    --primary-light: var(--md-grey-800);
    --primary-dark: var(--md-black);
    --secondary-color: var(--md-grey-600);
    --accent-color: var(--md-grey-900);

    /* Stat Card Colors */
    --blue-primary: #1976d2;
    --blue-light: #42a5f5;
    --blue-dark: #0d47a1;

    --green-primary: #388e3c;
    --green-light: #66bb6a;
    --green-dark: #1b5e20;

    --orange-primary: #f57c00;
    --orange-light: #ffb74d;
    --orange-dark: #e65100;

    --purple-primary: #7b1fa2;
    --purple-light: #ba68c8;
    --purple-dark: #4a148c;

    /* Surface Colors */
    --surface-color: var(--md-white);
    --background-color: var(--md-grey-50);
    --card-background: var(--md-white);

    /* Text Colors */
    --text-primary: var(--md-grey-900);
    --text-secondary: var(--md-grey-600);
    --text-disabled: var(--md-grey-400);
    --text-on-primary: var(--md-white);

    /* Border & Divider */
    --divider-color: var(--md-grey-300);
    --border-color: var(--md-grey-200);

    /* Material Design Elevation Shadows */
    --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --elevation-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    --elevation-4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
    --elevation-5: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);

    /* Material Design Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;

    /* Material Design Transitions */
    --transition-fast: 0.15s cubic-bezier(0.4, 0.0, 0.2, 1);
    --transition-standard: 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Material Design Typography */
.md-headline-1 { font-size: 96px; font-weight: 300; line-height: 1.167; }
.md-headline-2 { font-size: 60px; font-weight: 300; line-height: 1.2; }
.md-headline-3 { font-size: 48px; font-weight: 400; line-height: 1.167; }
.md-headline-4 { font-size: 34px; font-weight: 400; line-height: 1.235; }
.md-headline-5 { font-size: 24px; font-weight: 400; line-height: 1.334; }
.md-headline-6 { font-size: 20px; font-weight: 500; line-height: 1.6; }
.md-subtitle-1 { font-size: 16px; font-weight: 400; line-height: 1.75; }
.md-subtitle-2 { font-size: 14px; font-weight: 500; line-height: 1.57; }
.md-body-1 { font-size: 16px; font-weight: 400; line-height: 1.5; }
.md-body-2 { font-size: 14px; font-weight: 400; line-height: 1.43; }
.md-button { font-size: 14px; font-weight: 500; line-height: 1.75; text-transform: uppercase; }
.md-caption { font-size: 12px; font-weight: 400; line-height: 1.66; }
.md-overline { font-size: 10px; font-weight: 400; line-height: 2.66; text-transform: uppercase; }

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Material Design Navigation */
.navbar {
    background-color: var(--primary-color) !important;
    box-shadow: var(--elevation-2);
    z-index: 1100;
    border-bottom: none;
    height: 64px;
}

.navbar-brand {
    font-weight: 500;
    font-size: 20px;
    color: var(--text-on-primary) !important;
    letter-spacing: 0.15px;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.87) !important;
    font-weight: 500;
    padding: 8px 16px !important;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.12);
    color: var(--text-on-primary) !important;
}

.search-container .form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 8px 16px;
    background-color: var(--surface-color);
    color: var(--text-primary);
    transition: var(--transition-standard);
    font-size: 14px;
}

.search-container .form-control:focus {
    background-color: var(--surface-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.12);
    color: var(--text-primary);
}

.search-container .form-control::placeholder {
    color: var(--text-secondary);
}

/* Main Content */
.main-content {
    margin-top: 64px;
    height: calc(100vh - 64px);
    background-color: var(--background-color);
    overflow: hidden;
}

/* Material Design Sidebar */
.sidebar {
    background-color: var(--surface-color);
    border-right: 1px solid var(--divider-color);
    height: calc(100vh - 64px);
    padding: 0;
    box-shadow: var(--elevation-1);
    overflow: hidden;
    position: fixed;
    top: 64px;
    left: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

/* Fixed Sidebar Header */
.sidebar-header {
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
    padding: 16px;
    flex-shrink: 0;
    z-index: 1001;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

/* Scrollable Sidebar Content */
.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    position: relative;
}

.sidebar-title {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    letter-spacing: 0.15px;
}

/* Clear Filters Button in Header */
.sidebar-header .btn {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.sidebar-header .btn:hover {
    background-color: var(--md-grey-100);
    border-color: var(--md-grey-400);
    transform: translateY(-1px);
}

.filter-section {
    margin-bottom: 32px;
}

.filter-title {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 1.25px;
}

.filter-title i {
    font-size: 18px;
    color: var(--text-secondary);
    margin-right: 8px;
}

.filter-options {
    padding-left: 8px;
}

.form-check {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-right: 12px;
    border: 2px solid var(--text-secondary);
    border-radius: 2px;
    background-color: transparent;
    transition: var(--transition-fast);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.form-check-input:focus {
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.12);
}

.form-check-label {
    font-size: 14px;
    color: var(--text-primary);
    cursor: pointer;
    line-height: 1.43;
    user-select: none;
}

/* UOM Filter Colors */
.form-check-label[for="each"]::before {
    background: linear-gradient(135deg, var(--uom-each), #00796b);
}
.form-check-label[for="each"]:hover {
    background-color: var(--uom-each-light);
    color: var(--uom-each);
}

.form-check-label[for="piece"]::before {
    background: linear-gradient(135deg, var(--uom-piece), #afb42b);
}
.form-check-label[for="piece"]:hover {
    background-color: var(--uom-piece-light);
    color: var(--uom-piece);
}

.form-check-label[for="sqm"]::before {
    background: linear-gradient(135deg, var(--uom-sqm), #f57c00);
}
.form-check-label[for="sqm"]:hover {
    background-color: var(--uom-sqm-light);
    color: var(--uom-sqm);
}

.form-check-label[for="meter"]::before {
    background: linear-gradient(135deg, var(--uom-meter), #1976d2);
}
.form-check-label[for="meter"]:hover {
    background-color: var(--uom-meter-light);
    color: var(--uom-meter);
}

.form-check-label[for="kg"]::before {
    background: linear-gradient(135deg, var(--uom-kg), #7b1fa2);
}
.form-check-label[for="kg"]:hover {
    background-color: var(--uom-kg-light);
    color: var(--uom-kg);
}

/* Component Type Filter Colors */
.form-check-label[for="isComponent"]::before {
    background: linear-gradient(135deg, var(--component-yes), #388e3c);
}
.form-check-label[for="isComponent"]:hover {
    background-color: var(--component-yes-light);
    color: var(--component-yes);
}

.form-check-label[for="notComponent"]::before {
    background: linear-gradient(135deg, var(--component-no), #f57c00);
}
.form-check-label[for="notComponent"]:hover {
    background-color: var(--component-no-light);
    color: var(--component-no);
}

/* Status Filter Colors */
.form-check-label[for="active"]::before {
    background: linear-gradient(135deg, var(--status-active), #388e3c);
}
.form-check-label[for="active"]:hover {
    background-color: var(--status-active-light);
    color: var(--status-active);
}

.form-check-label[for="inactive"]::before {
    background: linear-gradient(135deg, var(--status-inactive), #d32f2f);
}
.form-check-label[for="inactive"]:hover {
    background-color: var(--status-inactive-light);
    color: var(--status-inactive);
}

/* Content Area */
.content-area {
    padding: 0;
    background-color: var(--background-color);
    height: calc(100vh - 64px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.content-header {
    padding: 16px 24px 8px 24px;
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
    flex-shrink: 0;
    box-shadow: var(--elevation-1);
}

.page-title {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 4px;
    letter-spacing: 0.25px;
}

/* Material Design Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Multiple Selection Controls - Modern Material Design 3 */
.selection-controls {
    display: none;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
    border: none;
    position: relative;
    overflow: hidden;
}

.selection-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.selection-controls.show {
    display: flex;
    animation: slideInUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.selection-info {
    font-size: 16px;
    font-weight: 600;
    color: var(--md-white);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1;
}

.selection-info::before {
    content: '';
    width: 20px;
    height: 20px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>') no-repeat center;
    background-size: contain;
    flex-shrink: 0;
}

.btn-delete-selected {
    background: linear-gradient(135deg, var(--status-inactive) 0%, #c62828 100%);
    color: var(--md-white);
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.75px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
    z-index: 1;
    position: relative;
}

.btn-delete-selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.btn-delete-selected:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(244, 67, 54, 0.4), 0 2px 4px rgba(244, 67, 54, 0.2);
}

.btn-delete-selected:hover::before {
    opacity: 1;
}

.btn-delete-selected:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.btn-delete-selected .material-icons {
    font-size: 18px;
}

/* Selection Checkbox Styles */
.selection-checkbox {
    position: absolute;
    top: 12px;
    left: 12px;
    width: 20px;
    height: 20px;
    z-index: 10;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.part-card:hover .selection-checkbox,
.part-item:hover .selection-checkbox,
.selection-checkbox:checked {
    opacity: 1;
}

.selection-checkbox:checked {
    accent-color: var(--primary-color);
}

/* Master Selection Checkbox */
.master-selection {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.master-selection input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.master-selection label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
}

.btn-group .btn {
    border: 1px solid var(--md-grey-300);
    background-color: var(--surface-color);
    color: var(--text-secondary);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    border-radius: 8px;
    padding: 8px 12px;
    min-width: 44px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.btn-group .btn.active {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.12);
}

.btn-group .btn:hover:not(.active) {
    background-color: var(--md-grey-100);
    border-color: var(--md-grey-400);
    color: var(--text-primary);
}

.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-on-primary) !important;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1.25px;
    padding: 12px 24px;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--elevation-2);
    transition: var(--transition-standard);
}

.btn-primary:hover {
    background-color: var(--primary-light) !important;
    border-color: var(--primary-light) !important;
    box-shadow: var(--elevation-3);
    transform: translateY(-2px);
}

/* Parts Container with Scrolling */
.parts-container-wrapper {
    flex: 1;
    overflow-y: auto;
    padding: 16px 24px;
    background-color: var(--background-color);
}

/* Material Design Stats Cards */
.stat-card {
    background: linear-gradient(135deg, var(--card-background) 0%, var(--md-grey-50) 100%);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.16);
    border: 1px solid var(--md-grey-200);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    height: 76px;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    /* Color will be set by specific card classes */
}

.stat-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.12), 0 2px 4px rgba(0,0,0,0.08);
    transform: translateY(-2px);
    border-color: var(--md-grey-300);
}

.stat-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-on-primary);
    font-size: 20px;
    flex-shrink: 0;
    position: relative;
}

/* Colored Stat Icons */
.stat-icon.bg-blue {
    background: linear-gradient(135deg, var(--blue-primary), var(--blue-light));
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.stat-icon.bg-green {
    background: linear-gradient(135deg, var(--green-primary), var(--green-light));
    box-shadow: 0 4px 12px rgba(56, 142, 60, 0.3);
}

.stat-icon.bg-orange {
    background: linear-gradient(135deg, var(--orange-primary), var(--orange-light));
    box-shadow: 0 4px 12px rgba(245, 124, 0, 0.3);
}

.stat-icon.bg-purple {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    box-shadow: 0 4px 12px rgba(123, 31, 162, 0.3);
}

/* Colored Stat Card Accents */
.stat-card-blue::before {
    background: linear-gradient(90deg, var(--blue-primary), var(--blue-light));
}

.stat-card-green::before {
    background: linear-gradient(90deg, var(--green-primary), var(--green-light));
}

.stat-card-orange::before {
    background: linear-gradient(90deg, var(--orange-primary), var(--orange-light));
}

.stat-card-purple::before {
    background: linear-gradient(90deg, var(--purple-primary), var(--purple-light));
}

.stat-content h3 {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 2px;
    color: var(--text-primary);
    line-height: 1.2;
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Material Design Parts Cards - Modern Compact Design */
.parts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.part-card {
    background-color: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.16);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow: hidden;
    cursor: pointer;
    border: 1px solid var(--md-grey-200);
    position: relative;
}

.part-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.12), 0 2px 4px rgba(0,0,0,0.08);
    transform: translateY(-2px);
    border-color: var(--md-grey-300);
}

.part-card-header {
    padding: 16px 20px 12px 20px;
    background: linear-gradient(135deg, var(--md-grey-50) 0%, var(--md-white) 100%);
    border-bottom: none;
}

.part-number {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 6px;
    letter-spacing: 0.25px;
    font-family: 'Roboto Mono', monospace;
}

.part-description {
    color: var(--text-primary);
    font-weight: 400;
    margin-bottom: 10px;
    line-height: 1.4;
    font-size: 13px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.part-category {
    display: inline-flex;
    align-items: center;
    background-color: var(--md-grey-100);
    color: var(--text-secondary);
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0.25px;
    border: 1px solid var(--md-grey-200);
}

.part-card-body {
    padding: 16px 20px 20px 20px;
}

.part-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.info-label {
    font-size: 10px;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 2px;
}

.info-value {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.3;
}

.component-badge {
    font-size: 10px;
    padding: 3px 8px;
    border-radius: 12px;
    width: fit-content;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
}

.component-badge.bg-success {
    background-color: var(--md-grey-900);
    color: var(--text-on-primary);
    border-color: var(--md-grey-900);
}

.component-badge.bg-secondary {
    background-color: var(--md-grey-600);
    color: var(--md-white);
    border-color: var(--md-grey-600);
}

.part-status {
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
}

.status-active {
    background-color: var(--md-grey-100);
    color: var(--md-grey-800);
    border-color: var(--md-grey-300);
}

.status-inactive {
    background-color: var(--md-grey-200);
    color: var(--md-grey-600);
    border-color: var(--md-grey-400);
}

.part-actions {
    display: flex;
    gap: 6px;
    justify-content: flex-end;
    margin-top: 4px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--md-grey-300);
    background-color: var(--surface-color);
    color: var(--text-secondary);
    transition: all 0.15s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
    font-size: 18px;
}

.btn-icon:hover {
    background-color: var(--md-grey-100);
    border-color: var(--md-grey-400);
    color: var(--text-primary);
    transform: scale(1.05);
}

.btn-view:hover {
    background-color: var(--md-grey-700);
    border-color: var(--md-grey-700);
    color: var(--text-on-primary);
}
.btn-edit:hover {
    background-color: var(--md-grey-600);
    border-color: var(--md-grey-600);
    color: var(--text-on-primary);
}
.btn-delete:hover {
    background-color: var(--md-grey-800);
    border-color: var(--md-grey-800);
    color: var(--text-on-primary);
}

/* Material Design List View */
.parts-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.part-item {
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--elevation-1);
    padding: 24px;
    transition: var(--transition-standard);
    cursor: pointer;
    border: 1px solid var(--border-color);
}

.part-item:hover {
    box-shadow: var(--elevation-3);
    transform: translateX(8px);
}

.function-group {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

/* Material Design Table View */
.table-container {
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--elevation-1);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.table {
    margin-bottom: 0;
    font-size: 14px;
}

.table thead th {
    background-color: var(--md-grey-100);
    border: none;
    font-weight: 500;
    color: var(--text-primary);
    padding: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 12px;
}

.table tbody td {
    padding: 16px;
    border-color: var(--divider-color);
    vertical-align: middle;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background-color: var(--md-grey-50);
}

.table tbody tr.selected {
    background-color: var(--md-grey-100);
    border-left: 3px solid var(--primary-color);
}

.table .selection-cell {
    width: 40px;
    padding: 8px !important;
}

.table .selection-cell input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

/* Material Design Pagination */
.pagination {
    justify-content: center;
    margin-top: 32px;
}

.pagination .page-link {
    border-radius: var(--border-radius-sm);
    margin: 0 4px;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    transition: var(--transition-standard);
    padding: 8px 12px;
    font-weight: 500;
    background-color: var(--surface-color);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-on-primary);
    box-shadow: var(--elevation-2);
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-on-primary);
    box-shadow: var(--elevation-2);
}

.pagination .page-item.disabled .page-link {
    background-color: var(--md-grey-200);
    border-color: var(--md-grey-200);
    color: var(--text-disabled);
}

/* Desktop Layout */
@media (min-width: 769px) {
    .content-area {
        margin-left: 20%;
        width: 80%;
    }

    .sidebar {
        width: 20%;
    }
}

/* Mobile Drawer Overlay */
.mobile-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-standard);
}

.mobile-drawer-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Material Design Responsive Design */
@media (max-width: 768px) {
    .main-content {
        margin-top: 64px;
        height: calc(100vh - 64px);
    }

    .sidebar {
        position: fixed;
        top: 64px;
        left: -100%;
        width: 280px;
        height: calc(100vh - 64px);
        background-color: var(--surface-color);
        box-shadow: var(--elevation-3);
        transition: var(--transition-standard);
        z-index: 1001;
    }

    .sidebar.show {
        left: 0;
    }

    .content-area {
        margin-left: 0;
        width: 100%;
    }

    .content-header {
        padding: 12px 16px 8px 16px;
    }

    .page-title {
        font-size: 20px;
    }

    .header-actions {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .btn-group {
        justify-content: center;
    }

    .parts-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .part-info-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .part-card-header {
        padding: 12px 16px 8px 16px;
    }

    .part-card-body {
        padding: 12px 16px 16px 16px;
    }

    .stat-card {
        padding: 12px;
        height: auto;
        gap: 8px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .stat-content h3 {
        font-size: 20px;
    }

    .filter-section {
        margin-bottom: 16px;
    }

    .filter-title {
        font-size: 12px;
        margin-bottom: 8px;
    }

    .parts-container-wrapper {
        padding: 12px 16px;
    }

    /* Mobile pagination styles */
    .pagination {
        gap: 4px;
        margin: 16px 0;
    }

    .page-link {
        padding: 10px 12px;
        min-width: 40px;
        height: 40px;
        font-size: 13px;
        border-radius: 10px;
    }

    .page-link .d-none.d-sm-inline {
        display: none !important;
    }

    .pagination-info {
        margin-top: 12px;
        padding: 8px 12px;
    }

    .pagination-info small {
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 18px;
    }

    .search-container {
        width: 100%;
        margin-bottom: 8px;
    }

    .part-card-header,
    .part-card-body {
        padding: 16px;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .table-container {
        overflow-x: auto;
    }

    .pagination .page-link {
        padding: 6px 10px;
        font-size: 12px;
    }
}

/* Material Design Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(24px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-24px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.slide-in-left {
    animation: slideInLeft 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.scale-in {
    animation: scaleIn 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Material Design Loading States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    flex-direction: column;
    gap: 16px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--md-grey-300);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Material Design Focus States */
.btn:focus,
.form-control:focus,
.form-check-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.12);
}

/* Material Design Utility Classes */
.elevation-0 { box-shadow: none; }
.elevation-1 { box-shadow: var(--elevation-1); }
.elevation-2 { box-shadow: var(--elevation-2); }
.elevation-3 { box-shadow: var(--elevation-3); }
.elevation-4 { box-shadow: var(--elevation-4); }
.elevation-5 { box-shadow: var(--elevation-5); }

.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-disabled { color: var(--text-disabled) !important; }

.bg-surface { background-color: var(--surface-color) !important; }
.bg-background { background-color: var(--background-color) !important; }

/* Modern Material Design 3 Pagination */
.pagination {
    justify-content: center;
    margin: 24px 0;
    gap: 8px;
    flex-wrap: wrap;
}

.page-item {
    margin: 0;
}

.page-link {
    color: var(--text-primary);
    background-color: var(--surface-color);
    border: 1px solid var(--md-grey-300);
    border-radius: 12px;
    padding: 12px 16px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.page-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.page-link:hover {
    color: var(--primary-color);
    background-color: var(--md-grey-50);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.page-link:hover::before {
    opacity: 1;
}

.page-item.active .page-link {
    color: var(--md-white);
    background: linear-gradient(135deg, var(--primary-color) 0%, #424242 100%);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
    font-weight: 600;
}

.page-item.active .page-link::before {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 1;
}

.page-item.disabled .page-link {
    color: var(--text-disabled);
    background-color: var(--md-grey-100);
    border-color: var(--md-grey-200);
    cursor: not-allowed;
    box-shadow: none;
}

.page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
    background-color: var(--md-grey-100);
    border-color: var(--md-grey-200);
}

.page-item.disabled .page-link::before {
    opacity: 0;
}

/* Navigation buttons (Previous/Next) */
.page-link[data-page]:not([data-page=""]) {
    gap: 6px;
}

.page-link .material-icons {
    font-size: 18px;
}

/* Pagination info text */
.pagination-info {
    text-align: center;
    margin-top: 16px;
    padding: 12px 16px;
    background-color: var(--md-grey-50);
    border-radius: 8px;
    border: 1px solid var(--md-grey-200);
}

.pagination-info small {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
    letter-spacing: 0.25px;
}
