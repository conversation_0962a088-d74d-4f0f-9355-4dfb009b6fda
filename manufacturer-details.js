// Manufacturer Details Page JavaScript

$(document).ready(function() {
    // Get manufacturer ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const manufacturerId = urlParams.get('id');
    const partId = urlParams.get('partId') || '0000052';

    if (manufacturerId) {
        loadManufacturerDetails(manufacturerId, partId);
    } else {
        showError('Manufacturer ID not provided');
    }
});

// Use the same JSON data structure as part-details.js
const partDetails = {
    '0000052': {
        id: '0000052',
        prefix: 'OEM',
        description: 'TWINE GUIDE',
        category: 'Accessory (Outside)',
        partType: 'Core',
        partFunctionGroup: 'Electrical',
        uom: 'Each',
        isComponent: true,
        isActive: true,
        manufacturerDetails: [
            {
                id: 'manufacturer_001',
                manufacturer: 'Bosch',
                prefix: 'BSH',
                manufacturerPart: 'F00RJ02697',
                buyingCurrency: 'USD',
                partnerNetPrice: 125.50,
                stdPackQty: 10,
                rushOrderYSO: 'Yes',
                lastInvoiceDate: '15-Nov-2024',
                effectiveFrom: '01-Jan-2024',
                manufacturerWarrantyDays: 365,
                isWarrantyLimitation: 'No',
                firstGrnDate: '20-Jan-2024',
                lastGrnDate: '01-Dec-2024',
                replenishmentOrderYR: 'Yes',
                stockOrderTEA: 'TEA001'
            },
            {
                id: 'manufacturer_002',
                manufacturer: 'Denso',
                prefix: 'DNS',
                manufacturerPart: '234-4567',
                buyingCurrency: 'EUR',
                partnerNetPrice: 89.75,
                stdPackQty: 5,
                rushOrderYSO: 'No',
                lastInvoiceDate: '28-Oct-2024',
                effectiveFrom: '01-Jun-2024',
                manufacturerWarrantyDays: 180,
                isWarrantyLimitation: 'Yes',
                firstGrnDate: '15-Jun-2024',
                lastGrnDate: '20-Nov-2024',
                replenishmentOrderYR: 'No',
                stockOrderTEA: 'TEA002'
            },
            {
                id: 'manufacturer_003',
                manufacturer: 'Continental',
                prefix: 'CON',
                manufacturerPart: 'CT-9876',
                buyingCurrency: 'USD',
                partnerNetPrice: 110.25,
                stdPackQty: 8,
                rushOrderYSO: 'Yes',
                lastInvoiceDate: '05-Dec-2024',
                effectiveFrom: '15-Mar-2024',
                manufacturerWarrantyDays: 270,
                isWarrantyLimitation: 'No',
                firstGrnDate: '20-Mar-2024',
                lastGrnDate: '10-Dec-2024',
                replenishmentOrderYR: 'Yes',
                stockOrderTEA: 'TEA003'
            }
        ]
    }
};

function loadManufacturerDetails(manufacturerId, partId) {
    try {
        const part = partDetails[partId];

        if (!part || !part.manufacturerDetails) {
            showError('Part or manufacturer details not found');
            return;
        }

        // Find the specific manufacturer entry
        const manufacturer = part.manufacturerDetails.find(m => m.id === manufacturerId);

        if (!manufacturer) {
            showError('Manufacturer entry not found');
            return;
        }

        // Update header information
        $('#partNumber').text(`${part.prefix}-${part.id}`);
        $('#partDescription').text(`${part.description} - Manufacturer Details`);
        $('#manufacturerName').text(manufacturer.manufacturer || '-');
        $('#manufacturerPart').text(`${manufacturer.prefix}-${manufacturer.manufacturerPart}` || '-');
        $('#partnerNetPrice').text(formatCurrency(manufacturer.partnerNetPrice));

        // Populate basic information
        $('#manufacturer').text(manufacturer.manufacturer || '-');
        $('#prefix').text(manufacturer.prefix || '-');
        $('#manufacturerPartDetail').text(manufacturer.manufacturerPart || '-');
        $('#buyingCurrency').text(manufacturer.buyingCurrency || '-');

        // Populate pricing information
        $('#partnerNetPriceDetail').text(formatCurrency(manufacturer.partnerNetPrice));
        $('#stdPackQty').text(formatNumber(manufacturer.stdPackQty));
        $('#rushOrderYSO').html(manufacturer.rushOrderYSO === 'Yes' ?
            '<span class="badge bg-success">Yes</span>' :
            '<span class="badge bg-secondary">No</span>');
        $('#lastInvoiceDate').text(manufacturer.lastInvoiceDate || '-');
        $('#effectiveFrom').text(manufacturer.effectiveFrom || '-');

        // Populate warranty information
        $('#manufacturerWarrantyDays').text(manufacturer.manufacturerWarrantyDays ?
            `${manufacturer.manufacturerWarrantyDays} days` : '-');
        $('#isWarrantyLimitation').html(manufacturer.isWarrantyLimitation === 'Yes' ?
            '<span class="badge bg-warning">Yes</span>' :
            '<span class="badge bg-success">No</span>');

        // Populate GRN information
        $('#firstGrnDate').text(manufacturer.firstGrnDate || '-');
        $('#lastGrnDate').text(manufacturer.lastGrnDate || '-');

        // Populate order information
        $('#replenishmentOrderYR').html(manufacturer.replenishmentOrderYR === 'Yes' ?
            '<span class="badge bg-success">Yes</span>' :
            '<span class="badge bg-secondary">No</span>');
        $('#stockOrderTEA').text(manufacturer.stockOrderTEA || '-');

        // Update quick stats
        $('#statManufacturer').text(manufacturer.manufacturer || '-');
        $('#statNetPrice').text(formatCurrency(manufacturer.partnerNetPrice));
        $('#statPackQty').text(manufacturer.stdPackQty || '0');
        $('#statWarranty').text(manufacturer.manufacturerWarrantyDays ? `${manufacturer.manufacturerWarrantyDays} days` : '-');

        // Update recent activity
        $('#lastManufacturerUpdate').text(manufacturer.effectiveFrom || 'Unknown');

        // Update page title
        document.title = `Manufacturer Details - ${manufacturer.manufacturer} - ${part.description}`;

        // Store current manufacturer data for actions
        window.currentManufacturer = manufacturer;
        window.currentPart = part;
        window.currentPartId = partId;

    } catch (error) {
        console.error('Error loading manufacturer details:', error);
        showError('Error loading manufacturer details');
    }
}

function formatNumber(value) {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    return Number(value).toLocaleString();
}

function formatCurrency(value) {
    if (value === null || value === undefined || value === '' || value === 0) {
        return '-';
    }
    return `$${Number(value).toFixed(2)}`;
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#manufacturer`;
    } else {
        window.location.href = 'part-details.html?id=0000052#manufacturer';
    }
}

function editManufacturer() {
    if (window.currentManufacturer) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for manufacturer ${window.currentManufacturer.manufacturer} will be implemented soon!`);
        console.log('Editing manufacturer:', window.currentManufacturer);
    }
}

function deleteManufacturer() {
    if (window.currentManufacturer) {
        const confirmed = confirm(`Are you sure you want to delete this manufacturer entry for ${window.currentManufacturer.manufacturer}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the manufacturer
            showSuccess(`Manufacturer entry for ${window.currentManufacturer.manufacturer} has been deleted!`);
            console.log('Deleting manufacturer:', window.currentManufacturer);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editManufacturer();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteManufacturer();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading manufacturer details...</p>
        </div>
    `;
    $('.details-grid').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
