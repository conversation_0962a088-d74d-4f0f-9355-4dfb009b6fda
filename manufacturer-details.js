// Manufacturer Details Page JavaScript

$(document).ready(function() {
    // Get manufacturer ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const manufacturerId = urlParams.get('id');
    const partId = urlParams.get('partId');
    
    if (manufacturerId) {
        loadManufacturerDetails(manufacturerId, partId);
    } else {
        showError('Manufacturer ID not provided');
    }
});

// Sample manufacturer data (in real application, this would come from API)
const manufacturerData = {
    'manufacturer_1': {
        id: 'manufacturer_1',
        manufacturer: 'Bosch',
        prefix: 'BSH',
        manufacturerPart: 'F00RJ02697',
        buyingCurrency: 'USD',
        partnerNetPrice: 125.50,
        stdPackQty: 10,
        rushOrderYSO: 'Yes',
        lastInvoiceDate: '2024-11-15',
        effectiveFrom: '2024-01-01',
        manufacturerWarrantyDays: 365,
        isWarrantyLimitation: 'No',
        firstGrnDate: '2024-01-20',
        lastGrnDate: '2024-12-01',
        replenishmentOrderYR: 'Yes',
        stockOrderTEA: 'TEA001'
    },
    'manufacturer_2': {
        id: 'manufacturer_2',
        manufacturer: 'Denso',
        prefix: 'DNS',
        manufacturerPart: '234-4567',
        buyingCurrency: 'EUR',
        partnerNetPrice: 89.75,
        stdPackQty: 5,
        rushOrderYSO: 'No',
        lastInvoiceDate: '2024-10-28',
        effectiveFrom: '2024-06-01',
        manufacturerWarrantyDays: 180,
        isWarrantyLimitation: 'Yes',
        firstGrnDate: '2024-06-15',
        lastGrnDate: '2024-11-20',
        replenishmentOrderYR: 'No',
        stockOrderTEA: 'TEA002'
    }
};

function loadManufacturerDetails(manufacturerId, partId) {
    try {
        const manufacturer = manufacturerData[manufacturerId];
        
        if (!manufacturer) {
            showError('Manufacturer not found');
            return;
        }
        
        // Populate basic information
        $('#manufacturer').text(manufacturer.manufacturer || '-');
        $('#prefix').text(manufacturer.prefix || '-');
        $('#manufacturerPart').text(manufacturer.manufacturerPart || '-');
        $('#buyingCurrency').text(manufacturer.buyingCurrency || '-');
        
        // Populate pricing information
        $('#partnerNetPrice').text(formatCurrency(manufacturer.partnerNetPrice));
        $('#stdPackQty').text(formatNumber(manufacturer.stdPackQty));
        $('#rushOrderYSO').html(manufacturer.rushOrderYSO === 'Yes' ? 
            '<span class="badge bg-success">Yes</span>' : 
            '<span class="badge bg-secondary">No</span>');
        $('#lastInvoiceDate').text(manufacturer.lastInvoiceDate || '-');
        $('#effectiveFrom').text(manufacturer.effectiveFrom || '-');
        
        // Populate warranty information
        $('#manufacturerWarrantyDays').text(manufacturer.manufacturerWarrantyDays ? 
            `${manufacturer.manufacturerWarrantyDays} days` : '-');
        $('#isWarrantyLimitation').html(manufacturer.isWarrantyLimitation === 'Yes' ? 
            '<span class="badge bg-warning">Yes</span>' : 
            '<span class="badge bg-success">No</span>');
        
        // Populate GRN information
        $('#firstGrnDate').text(manufacturer.firstGrnDate || '-');
        $('#lastGrnDate').text(manufacturer.lastGrnDate || '-');
        
        // Populate order information
        $('#replenishmentOrderYR').html(manufacturer.replenishmentOrderYR === 'Yes' ? 
            '<span class="badge bg-success">Yes</span>' : 
            '<span class="badge bg-secondary">No</span>');
        $('#stockOrderTEA').text(manufacturer.stockOrderTEA || '-');
        
        // Update page title
        document.title = `Manufacturer Details - ${manufacturer.manufacturer} - Parts Management System`;
        
        // Store current manufacturer data for actions
        window.currentManufacturer = manufacturer;
        window.currentPartId = partId;
        
    } catch (error) {
        console.error('Error loading manufacturer details:', error);
        showError('Error loading manufacturer details');
    }
}

function formatNumber(value) {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    return Number(value).toLocaleString();
}

function formatCurrency(value) {
    if (value === null || value === undefined || value === '' || value === 0) {
        return '-';
    }
    return `$${Number(value).toFixed(2)}`;
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.details-container').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#manufacturer`;
    } else {
        window.location.href = 'part-details.html#manufacturer';
    }
}

function editManufacturer() {
    if (window.currentManufacturer) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for manufacturer ${window.currentManufacturer.manufacturer} will be implemented soon!`);
        console.log('Editing manufacturer:', window.currentManufacturer);
    }
}

function deleteManufacturer() {
    if (window.currentManufacturer) {
        const confirmed = confirm(`Are you sure you want to delete this manufacturer entry for ${window.currentManufacturer.manufacturer}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the manufacturer
            showSuccess(`Manufacturer entry for ${window.currentManufacturer.manufacturer} has been deleted!`);
            console.log('Deleting manufacturer:', window.currentManufacturer);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editManufacturer();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteManufacturer();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading manufacturer details...</p>
        </div>
    `;
    $('.details-grid').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
