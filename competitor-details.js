// Competitor Details Page JavaScript

$(document).ready(function() {
    // Get competitor ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const competitorId = urlParams.get('id');
    const partId = urlParams.get('partId') || '0000052';
    
    if (competitorId) {
        loadCompetitorDetails(competitorId, partId);
    } else {
        showError('Competitor ID not provided');
    }
});

// Use the same JSON data structure as part-details.js
const partDetails = {
    '0000052': {
        id: '0000052',
        prefix: 'OEM',
        description: 'TWINE GUIDE',
        category: 'Accessory (Outside)',
        partType: 'Core',
        partFunctionGroup: 'Electrical',
        uom: 'Each',
        isComponent: true,
        isActive: true,
        competitorPriceDetails: [
            {
                id: 'comp_001',
                competitorName: 'AutoZone Parts',
                netRate: 90.00,
                costPrice: 85.00,
                effectiveFrom: '01-Sep-2023',
                remarks: 'Special discount applied for bulk orders',
                modifiedBy: 'admin',
                modifiedDate: '10-Sep-2023',
                currency: 'USD',
                availability: 'In Stock',
                leadTime: '3-5 days',
                minimumOrderQty: 10,
                contactInfo: '<EMAIL>',
                lastUpdated: '15-Sep-2023'
            },
            {
                id: 'comp_002',
                competitorName: 'NAPA Auto Parts',
                netRate: 92.00,
                costPrice: 88.00,
                effectiveFrom: '01-Oct-2023',
                remarks: 'Standard pricing with warranty',
                modifiedBy: 'user1',
                modifiedDate: '05-Oct-2023',
                currency: 'USD',
                availability: 'Limited Stock',
                leadTime: '7-10 days',
                minimumOrderQty: 5,
                contactInfo: '<EMAIL>',
                lastUpdated: '08-Oct-2023'
            },
            {
                id: 'comp_003',
                competitorName: 'O\'Reilly Auto Parts',
                netRate: 87.50,
                costPrice: 82.00,
                effectiveFrom: '15-Aug-2023',
                remarks: 'Competitive pricing with fast shipping',
                modifiedBy: 'manager',
                modifiedDate: '20-Aug-2023',
                currency: 'USD',
                availability: 'In Stock',
                leadTime: '2-4 days',
                minimumOrderQty: 1,
                contactInfo: '<EMAIL>',
                lastUpdated: '25-Aug-2023'
            },
            {
                id: 'comp_004',
                competitorName: 'Advance Auto Parts',
                netRate: 94.75,
                costPrice: 90.25,
                effectiveFrom: '01-Jul-2023',
                remarks: 'Premium quality with extended warranty',
                modifiedBy: 'supervisor',
                modifiedDate: '05-Jul-2023',
                currency: 'USD',
                availability: 'In Stock',
                leadTime: '1-3 days',
                minimumOrderQty: 2,
                contactInfo: '<EMAIL>',
                lastUpdated: '10-Jul-2023'
            },
            {
                id: 'comp_005',
                competitorName: 'Parts Authority',
                netRate: 96.00,
                costPrice: 91.50,
                effectiveFrom: '15-Jun-2023',
                remarks: 'OEM quality replacement parts',
                modifiedBy: 'admin',
                modifiedDate: '20-Jun-2023',
                currency: 'USD',
                availability: 'Back Order',
                leadTime: '14-21 days',
                minimumOrderQty: 25,
                contactInfo: '<EMAIL>',
                lastUpdated: '25-Jun-2023'
            },
            {
                id: 'comp_006',
                competitorName: 'Euro Car Parts',
                netRate: 78.50,
                costPrice: 74.00,
                effectiveFrom: '01-May-2023',
                remarks: 'European supplier with competitive rates',
                modifiedBy: 'procurement',
                modifiedDate: '05-May-2023',
                currency: 'EUR',
                availability: 'In Stock',
                leadTime: '5-7 days',
                minimumOrderQty: 15,
                contactInfo: '<EMAIL>',
                lastUpdated: '10-May-2023'
            }
        ]
    }
};

function loadCompetitorDetails(competitorId, partId) {
    try {
        const part = partDetails[partId];
        
        if (!part || !part.competitorPriceDetails) {
            showError('Part or competitor details not found');
            return;
        }
        
        // Find the specific competitor entry
        const competitor = part.competitorPriceDetails.find(c => c.id === competitorId);
        
        if (!competitor) {
            showError('Competitor entry not found');
            return;
        }
        
        // Update header information
        $('#partNumber').text(`${part.prefix}-${part.id}`);
        $('#partDescription').text(`${part.description} - Competitor Details`);
        $('#competitorName').text(competitor.competitorName || '-');
        $('#netRate').text(formatCurrency(competitor.netRate));
        $('#availability').text(competitor.availability || '-');
        
        // Populate competitor information
        $('#competitorNameDetail').text(competitor.competitorName || '-');
        $('#netRateDetail').text(formatCurrency(competitor.netRate));
        $('#costPrice').text(formatCurrency(competitor.costPrice));
        $('#currency').text(competitor.currency || '-');
        $('#effectiveFrom').text(competitor.effectiveFrom || '-');
        
        // Populate additional details
        $('#availabilityDetail').html(getAvailabilityBadge(competitor.availability));
        $('#leadTime').text(competitor.leadTime || '-');
        $('#minimumOrderQty').text(formatNumber(competitor.minimumOrderQty));
        $('#contactInfo').text(competitor.contactInfo || '-');
        $('#lastUpdated').text(competitor.lastUpdated || '-');
        $('#remarks').text(competitor.remarks || '-');
        
        // Update quick stats
        $('#statNetRate').text(formatCurrency(competitor.netRate));
        $('#statAvailability').text(competitor.availability || '-');
        $('#statLeadTime').text(competitor.leadTime || '-');
        $('#statMinOrder').text(formatNumber(competitor.minimumOrderQty));
        
        // Update recent activity
        $('#lastPriceUpdate').text(competitor.lastUpdated || 'Unknown');
        
        // Update page title
        document.title = `Competitor Details - ${competitor.competitorName} - ${part.description}`;
        
        // Store current competitor data for actions
        window.currentCompetitor = competitor;
        window.currentPart = part;
        window.currentPartId = partId;
        
    } catch (error) {
        console.error('Error loading competitor details:', error);
        showError('Error loading competitor details');
    }
}

function getAvailabilityBadge(availability) {
    switch (availability) {
        case 'In Stock':
            return '<span class="badge bg-success">In Stock</span>';
        case 'Limited Stock':
            return '<span class="badge bg-warning">Limited Stock</span>';
        case 'Back Order':
            return '<span class="badge bg-danger">Back Order</span>';
        default:
            return '<span class="badge bg-secondary">Unknown</span>';
    }
}

function formatNumber(value) {
    if (value === null || value === undefined || value === '') {
        return '-';
    }
    return Number(value).toLocaleString();
}

function formatCurrency(value) {
    if (value === null || value === undefined || value === '' || value === 0) {
        return '-';
    }
    return `$${Number(value).toFixed(2)}`;
}

function showError(message) {
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(errorHtml);
}

function showSuccess(message) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(successHtml);
}

// Action functions
function goBack() {
    if (window.currentPartId) {
        window.location.href = `part-details.html?id=${window.currentPartId}#competitorPriceDetails`;
    } else {
        window.location.href = 'part-details.html?id=0000052#competitorPriceDetails';
    }
}

function editCompetitor() {
    if (window.currentCompetitor) {
        // In a real application, this would open an edit modal or navigate to edit page
        showSuccess(`Edit functionality for competitor ${window.currentCompetitor.competitorName} will be implemented soon!`);
        console.log('Editing competitor:', window.currentCompetitor);
    }
}

function deleteCompetitor() {
    if (window.currentCompetitor) {
        const confirmed = confirm(`Are you sure you want to delete this competitor entry for ${window.currentCompetitor.competitorName}?`);
        if (confirmed) {
            // In a real application, this would make an API call to delete the competitor
            showSuccess(`Competitor entry for ${window.currentCompetitor.competitorName} has been deleted!`);
            console.log('Deleting competitor:', window.currentCompetitor);
            
            // Redirect back after a delay
            setTimeout(() => {
                goBack();
            }, 2000);
        }
    }
}

// Keyboard shortcuts
$(document).keydown(function(e) {
    // ESC key to go back
    if (e.keyCode === 27) {
        goBack();
    }
    
    // Ctrl+E to edit
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        editCompetitor();
    }
    
    // Delete key to delete (with confirmation)
    if (e.keyCode === 46) {
        deleteCompetitor();
    }
});

// Add loading animation
function showLoading() {
    const loadingHtml = `
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading competitor details...</p>
        </div>
    `;
    $('.main-content').html(loadingHtml);
}

function hideLoading() {
    $('#loadingIndicator').remove();
}
