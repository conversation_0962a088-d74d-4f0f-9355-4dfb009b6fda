<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Details - Parts Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="part-details.css">
    
    <style>
        .details-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .details-header {
            background: linear-gradient(135deg, var(--md-surface) 0%, var(--md-surface-container) 100%);
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: var(--md-elevation-2);
            border: 1px solid var(--md-outline-variant);
        }
        
        .details-title {
            font-size: var(--md-typescale-headline-medium);
            font-weight: 600;
            color: var(--md-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .details-subtitle {
            font-size: var(--md-typescale-body-large);
            color: var(--md-on-surface-variant);
            margin-bottom: 24px;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        
        .details-card {
            background: var(--md-surface);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--md-elevation-1);
            border: 1px solid var(--md-outline-variant);
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-standard);
        }
        
        .details-card:hover {
            box-shadow: var(--md-elevation-2);
            transform: translateY(-2px);
        }
        
        .details-card-title {
            font-size: var(--md-typescale-title-medium);
            font-weight: 600;
            color: var(--md-on-surface);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .details-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--md-outline-variant);
        }
        
        .details-field:last-child {
            border-bottom: none;
        }
        
        .details-label {
            font-size: var(--md-typescale-body-medium);
            font-weight: 500;
            color: var(--md-on-surface-variant);
        }
        
        .details-value {
            font-size: var(--md-typescale-body-medium);
            font-weight: 600;
            color: var(--md-on-surface);
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 32px;
        }
        
        .btn-modern {
            border-radius: 20px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-emphasized);
            box-shadow: var(--md-elevation-1);
        }
        
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--md-elevation-2);
        }
        
        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin-bottom: 24px;
        }
        
        .breadcrumb-modern .breadcrumb-item a {
            color: var(--md-on-surface-variant);
            text-decoration: none;
            transition: color var(--md-motion-duration-short2) var(--md-motion-easing-standard);
        }
        
        .breadcrumb-modern .breadcrumb-item a:hover {
            color: var(--md-primary);
        }
        
        .breadcrumb-modern .breadcrumb-item.active {
            color: var(--md-on-surface);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="details-container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-modern">
                <li class="breadcrumb-item"><a href="part-details.html">Part Details</a></li>
                <li class="breadcrumb-item"><a href="part-details.html#pricing">Pricing</a></li>
                <li class="breadcrumb-item active" aria-current="page">Pricing Details</li>
            </ol>
        </nav>
        
        <!-- Header -->
        <div class="details-header">
            <h1 class="details-title">
                <i class="material-icons">attach_money</i>
                Pricing Details
            </h1>
            <p class="details-subtitle">Comprehensive pricing information and cost details</p>
        </div>
        
        <!-- Details Grid -->
        <div class="details-grid">
            <!-- Basic Pricing Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">info</i>
                    Basic Information
                </h3>
                <div class="details-field">
                    <span class="details-label">Effective From</span>
                    <span class="details-value" id="effectiveFrom">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Effective To</span>
                    <span class="details-value" id="effectiveTo">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Currency</span>
                    <span class="details-value" id="currency">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Status</span>
                    <span class="details-value" id="status">-</span>
                </div>
            </div>
            
            <!-- Price Details -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">monetization_on</i>
                    Price Information
                </h3>
                <div class="details-field">
                    <span class="details-label">List Price</span>
                    <span class="details-value" id="listPrice">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Net Price</span>
                    <span class="details-value" id="netPrice">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Discount %</span>
                    <span class="details-value" id="discountPercent">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Cost Price</span>
                    <span class="details-value" id="costPrice">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Margin %</span>
                    <span class="details-value" id="marginPercent">-</span>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">description</i>
                    Additional Details
                </h3>
                <div class="details-field">
                    <span class="details-label">Price Type</span>
                    <span class="details-value" id="priceType">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Minimum Quantity</span>
                    <span class="details-value" id="minimumQuantity">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Maximum Quantity</span>
                    <span class="details-value" id="maximumQuantity">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Created Date</span>
                    <span class="details-value" id="createdDate">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Last Modified</span>
                    <span class="details-value" id="lastModified">-</span>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-outline-secondary btn-modern" onclick="goBack()">
                <i class="material-icons me-2">arrow_back</i>
                Back to Part Details
            </button>
            <button class="btn btn-primary btn-modern" onclick="editPricing()">
                <i class="material-icons me-2">edit</i>
                Edit Pricing
            </button>
            <button class="btn btn-outline-danger btn-modern" onclick="deletePricing()">
                <i class="material-icons me-2">delete</i>
                Delete Pricing
            </button>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="pricing-details.js"></script>
</body>
</html>
