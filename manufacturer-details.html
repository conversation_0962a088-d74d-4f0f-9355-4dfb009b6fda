<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manufacturer Details - Parts Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="part-details.css">
    
    <style>
        .details-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .details-header {
            background: linear-gradient(135deg, var(--md-surface) 0%, var(--md-surface-container) 100%);
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: var(--md-elevation-2);
            border: 1px solid var(--md-outline-variant);
        }
        
        .details-title {
            font-size: var(--md-typescale-headline-medium);
            font-weight: 600;
            color: var(--md-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .details-subtitle {
            font-size: var(--md-typescale-body-large);
            color: var(--md-on-surface-variant);
            margin-bottom: 24px;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
        }
        
        .details-card {
            background: var(--md-surface);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--md-elevation-1);
            border: 1px solid var(--md-outline-variant);
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-standard);
        }
        
        .details-card:hover {
            box-shadow: var(--md-elevation-2);
            transform: translateY(-2px);
        }
        
        .details-card-title {
            font-size: var(--md-typescale-title-medium);
            font-weight: 600;
            color: var(--md-on-surface);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .details-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--md-outline-variant);
        }
        
        .details-field:last-child {
            border-bottom: none;
        }
        
        .details-label {
            font-size: var(--md-typescale-body-medium);
            font-weight: 500;
            color: var(--md-on-surface-variant);
            flex: 1;
        }
        
        .details-value {
            font-size: var(--md-typescale-body-medium);
            font-weight: 600;
            color: var(--md-on-surface);
            flex: 1;
            text-align: right;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 32px;
        }
        
        .btn-modern {
            border-radius: 20px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all var(--md-motion-duration-medium1) var(--md-motion-easing-emphasized);
            box-shadow: var(--md-elevation-1);
        }
        
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--md-elevation-2);
        }
        
        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin-bottom: 24px;
        }
        
        .breadcrumb-modern .breadcrumb-item a {
            color: var(--md-on-surface-variant);
            text-decoration: none;
            transition: color var(--md-motion-duration-short2) var(--md-motion-easing-standard);
        }
        
        .breadcrumb-modern .breadcrumb-item a:hover {
            color: var(--md-primary);
        }
        
        .breadcrumb-modern .breadcrumb-item.active {
            color: var(--md-on-surface);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Section - Same as part-details.html -->
        <div class="part-header">
            <div class="part-header-content">
                <div class="part-header-left">
                    <div class="part-number-section">
                        <span class="part-number" id="partNumber">Loading...</span>
                        <span class="part-status-badge status-active" id="partStatus">Active</span>
                    </div>
                    <h1 class="part-description" id="partDescription">Manufacturer Details</h1>
                    <div class="part-meta">
                        <div class="meta-item">
                            <span class="meta-label">Manufacturer:</span>
                            <span class="meta-value" id="manufacturerName">Loading...</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Part Number:</span>
                            <span class="meta-value" id="manufacturerPart">Loading...</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">Net Price:</span>
                            <span class="meta-value" id="partnerNetPrice">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="part-header-right">
                    <div class="header-actions">
                        <button class="btn btn-outline-secondary" onclick="goBack()">
                            <i class="material-icons me-2">arrow_back</i>
                            Back to Part Details
                        </button>
                        <button class="btn btn-primary" onclick="editManufacturer()">
                            <i class="material-icons me-2">edit</i>
                            Edit Manufacturer
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteManufacturer()">
                            <i class="material-icons me-2">delete</i>
                            Delete Manufacturer
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Details Grid -->
        <div class="details-grid">
            <!-- Basic Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">info</i>
                    Basic Information
                </h3>
                <div class="details-field">
                    <span class="details-label">Manufacturer</span>
                    <span class="details-value" id="manufacturer">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Prefix</span>
                    <span class="details-value" id="prefix">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Manufacturer Part</span>
                    <span class="details-value" id="manufacturerPart">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Buying Currency</span>
                    <span class="details-value" id="buyingCurrency">-</span>
                </div>
            </div>
            
            <!-- Pricing Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">monetization_on</i>
                    Pricing Information
                </h3>
                <div class="details-field">
                    <span class="details-label">Partner Net Price</span>
                    <span class="details-value" id="partnerNetPrice">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Std. Pack Qty</span>
                    <span class="details-value" id="stdPackQty">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Rush Order (Y/SO)</span>
                    <span class="details-value" id="rushOrderYSO">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Last Invoiced Date</span>
                    <span class="details-value" id="lastInvoiceDate">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Effective From</span>
                    <span class="details-value" id="effectiveFrom">-</span>
                </div>
            </div>
            
            <!-- Warranty Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">verified</i>
                    Warranty Information
                </h3>
                <div class="details-field">
                    <span class="details-label">Manf. Warr. (Days)</span>
                    <span class="details-value" id="manufacturerWarrantyDays">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Is Warranty Limitation</span>
                    <span class="details-value" id="isWarrantyLimitation">-</span>
                </div>
            </div>
            
            <!-- GRN Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">receipt</i>
                    GRN Information
                </h3>
                <div class="details-field">
                    <span class="details-label">First GRN Date</span>
                    <span class="details-value" id="firstGrnDate">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Last GRN Date</span>
                    <span class="details-value" id="lastGrnDate">-</span>
                </div>
            </div>
            
            <!-- Order Information -->
            <div class="details-card">
                <h3 class="details-card-title">
                    <i class="material-icons">shopping_cart</i>
                    Order Information
                </h3>
                <div class="details-field">
                    <span class="details-label">Replenishment Order (Y/R)</span>
                    <span class="details-value" id="replenishmentOrderYR">-</span>
                </div>
                <div class="details-field">
                    <span class="details-label">Stock Order (TEA)</span>
                    <span class="details-value" id="stockOrderTEA">-</span>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-outline-secondary btn-modern" onclick="goBack()">
                <i class="material-icons me-2">arrow_back</i>
                Back to Part Details
            </button>
            <button class="btn btn-primary btn-modern" onclick="editManufacturer()">
                <i class="material-icons me-2">edit</i>
                Edit Manufacturer
            </button>
            <button class="btn btn-outline-danger btn-modern" onclick="deleteManufacturer()">
                <i class="material-icons me-2">delete</i>
                Delete Manufacturer
            </button>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="manufacturer-details.js"></script>
</body>
</html>
